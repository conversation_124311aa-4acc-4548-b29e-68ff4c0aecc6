"""
Gestionnaire de base de données clients pour BH Assurance
Simule une vraie base de données avec clients, contrats, sinistres
"""

import sqlite3
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import random

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self, db_path: str = "bh_assurance_clients.db"):
        self.db_path = db_path
        self._init_database()
        self._populate_sample_data()
    
    def _init_database(self):
        """Initialise la base de données avec les tables nécessaires"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Table des clients
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS clients (
                        client_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        nom TEXT NOT NULL,
                        prenom TEXT NOT NULL,
                        email TEXT UNIQUE,
                        telephone TEXT,
                        date_naissance DATE,
                        adresse TEXT,
                        cin TEXT UNIQUE,
                        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        statut TEXT DEFAULT 'actif'
                    )
                ''')
                
                # Table des produits d'assurance
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS produits (
                        produit_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        nom TEXT NOT NULL,
                        branche TEXT NOT NULL,
                        sous_branche TEXT,
                        description TEXT,
                        tarif_base REAL,
                        conditions_generales TEXT
                    )
                ''')
                
                # Table des contrats
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS contrats (
                        contrat_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        client_id INTEGER,
                        produit_id INTEGER,
                        numero_contrat TEXT UNIQUE,
                        date_souscription DATE,
                        date_debut DATE,
                        date_fin DATE,
                        prime_annuelle REAL,
                        statut TEXT DEFAULT 'actif',
                        formule TEXT,
                        franchise REAL,
                        plafond_garantie REAL,
                        FOREIGN KEY (client_id) REFERENCES clients (client_id),
                        FOREIGN KEY (produit_id) REFERENCES produits (produit_id)
                    )
                ''')
                
                # Table des garanties
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS garanties (
                        garantie_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        contrat_id INTEGER,
                        nom_garantie TEXT,
                        montant_couvert REAL,
                        franchise REAL,
                        conditions TEXT,
                        FOREIGN KEY (contrat_id) REFERENCES contrats (contrat_id)
                    )
                ''')
                
                # Table des sinistres
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sinistres (
                        sinistre_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        contrat_id INTEGER,
                        numero_sinistre TEXT UNIQUE,
                        date_sinistre DATE,
                        date_declaration DATE,
                        type_sinistre TEXT,
                        description TEXT,
                        montant_dommage REAL,
                        montant_indemnise REAL,
                        statut TEXT DEFAULT 'en_cours',
                        expert_assigne TEXT,
                        FOREIGN KEY (contrat_id) REFERENCES contrats (contrat_id)
                    )
                ''')
                
                # Table des paiements
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS paiements (
                        paiement_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        contrat_id INTEGER,
                        montant REAL,
                        date_echeance DATE,
                        date_paiement DATE,
                        statut TEXT DEFAULT 'en_attente',
                        mode_paiement TEXT,
                        FOREIGN KEY (contrat_id) REFERENCES contrats (contrat_id)
                    )
                ''')
                
                conn.commit()
                logger.info("Base de données initialisée avec succès")
                
        except Exception as e:
            logger.error(f"Erreur lors de l'initialisation de la base de données: {e}")
    
    def _populate_sample_data(self):
        """Peuple la base avec des données d'exemple"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Vérifier si les données existent déjà
                cursor.execute("SELECT COUNT(*) FROM clients")
                if cursor.fetchone()[0] > 0:
                    return  # Données déjà présentes
                
                # Insérer des produits d'assurance
                produits = [
                    ("Assurance Auto Essentielle", "Auto", "Véhicules particuliers", "Responsabilité civile obligatoire", 200.0, "Conditions générales auto standard"),
                    ("Assurance Auto Tous Risques", "Auto", "Véhicules particuliers", "Protection complète du véhicule", 800.0, "Conditions générales auto premium"),
                    ("Assurance Santé Individuelle", "Santé", "Personnes physiques", "Couverture médicale complète", 300.0, "Conditions générales santé"),
                    ("Assurance Habitation Standard", "Habitation", "Logements", "Protection du logement et mobilier", 150.0, "Conditions générales habitation"),
                    ("Assurance Vie Temporaire", "Vie", "Prévoyance", "Capital décès temporaire", 500.0, "Conditions générales vie"),
                ]
                
                cursor.executemany('''
                    INSERT INTO produits (nom, branche, sous_branche, description, tarif_base, conditions_generales)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', produits)
                
                # Insérer des clients d'exemple
                clients = [
                    ("Ben Ali", "Ahmed", "<EMAIL>", "+216 20 123 456", "1985-03-15", "Rue de la République, Tunis", "12345678"),
                    ("Trabelsi", "Fatma", "<EMAIL>", "+216 25 789 012", "1990-07-22", "Avenue Bourguiba, Sfax", "87654321"),
                    ("Khelifi", "Mohamed", "<EMAIL>", "+216 22 345 678", "1978-11-08", "Boulevard du 7 Novembre, Sousse", "11223344"),
                    ("Sassi", "Leila", "<EMAIL>", "+216 26 901 234", "1992-05-30", "Rue Ibn Khaldoun, Monastir", "44332211"),
                    ("Gharbi", "Karim", "<EMAIL>", "+216 23 567 890", "1988-12-12", "Avenue de la Liberté, Bizerte", "55667788"),
                ]
                
                cursor.executemany('''
                    INSERT INTO clients (nom, prenom, email, telephone, date_naissance, adresse, cin)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', clients)
                
                # Insérer des contrats
                contrats = [
                    (1, 1, "AUTO001", "2024-01-15", "2024-01-15", "2025-01-15", 250.0, "actif", "essentielle", 200.0, 50000.0),
                    (1, 4, "HAB001", "2024-02-01", "2024-02-01", "2025-02-01", 180.0, "actif", "standard", 150.0, 100000.0),
                    (2, 2, "AUTO002", "2024-01-20", "2024-01-20", "2025-01-20", 850.0, "actif", "tous_risques", 300.0, 80000.0),
                    (3, 3, "SANTE001", "2024-03-01", "2024-03-01", "2025-03-01", 320.0, "actif", "individuelle", 50.0, 200000.0),
                    (4, 5, "VIE001", "2024-01-10", "2024-01-10", "2034-01-10", 520.0, "actif", "temporaire", 0.0, 500000.0),
                ]
                
                cursor.executemany('''
                    INSERT INTO contrats (client_id, produit_id, numero_contrat, date_souscription, 
                                        date_debut, date_fin, prime_annuelle, statut, formule, 
                                        franchise, plafond_garantie)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', contrats)
                
                # Insérer des garanties
                garanties = [
                    (1, "Responsabilité Civile", 50000.0, 200.0, "Dommages causés aux tiers"),
                    (2, "Incendie", 100000.0, 150.0, "Dommages par incendie"),
                    (2, "Vol", 50000.0, 150.0, "Vol avec effraction"),
                    (3, "Collision", 80000.0, 300.0, "Dommages collision"),
                    (3, "Vol/Incendie", 80000.0, 300.0, "Vol et incendie véhicule"),
                    (4, "Hospitalisation", 200000.0, 50.0, "Frais d'hospitalisation"),
                    (4, "Soins ambulatoires", 50000.0, 20.0, "Consultations et soins"),
                    (5, "Capital Décès", 500000.0, 0.0, "Capital versé en cas de décès"),
                ]
                
                cursor.executemany('''
                    INSERT INTO garanties (contrat_id, nom_garantie, montant_couvert, franchise, conditions)
                    VALUES (?, ?, ?, ?, ?)
                ''', garanties)
                
                # Insérer des sinistres
                sinistres = [
                    (1, "SIN001", "2024-06-15", "2024-06-16", "Accident", "Collision avec un autre véhicule", 3500.0, 3300.0, "regle", "Expert Auto Tunis"),
                    (3, "SIN002", "2024-08-20", "2024-08-21", "Vol", "Vol du véhicule dans un parking", 25000.0, 24700.0, "en_cours", "Expert Auto Sfax"),
                    (2, "SIN003", "2024-09-10", "2024-09-12", "Incendie", "Incendie partiel de l'habitation", 8000.0, 7850.0, "expertise", "Expert Habitation"),
                ]
                
                cursor.executemany('''
                    INSERT INTO sinistres (contrat_id, numero_sinistre, date_sinistre, date_declaration,
                                         type_sinistre, description, montant_dommage, montant_indemnise,
                                         statut, expert_assigne)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', sinistres)
                
                # Insérer des paiements
                paiements = [
                    (1, 250.0, "2024-01-15", "2024-01-15", "paye", "Virement"),
                    (1, 250.0, "2025-01-15", None, "en_attente", "Virement"),
                    (2, 180.0, "2024-02-01", "2024-02-01", "paye", "Chèque"),
                    (3, 850.0, "2024-01-20", "2024-01-20", "paye", "Carte bancaire"),
                    (4, 320.0, "2024-03-01", "2024-03-05", "paye", "Virement"),
                    (5, 520.0, "2024-01-10", "2024-01-10", "paye", "Prélèvement"),
                ]
                
                cursor.executemany('''
                    INSERT INTO paiements (contrat_id, montant, date_echeance, date_paiement, statut, mode_paiement)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', paiements)
                
                conn.commit()
                logger.info("Données d'exemple insérées avec succès")
                
        except Exception as e:
            logger.error(f"Erreur lors de l'insertion des données d'exemple: {e}")
    
    def get_client_by_identifier(self, identifier: str) -> Optional[Dict]:
        """Recherche un client par email, téléphone ou CIN"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM clients 
                    WHERE email = ? OR telephone = ? OR cin = ?
                ''', (identifier, identifier, identifier))
                
                row = cursor.fetchone()
                if row:
                    columns = [desc[0] for desc in cursor.description]
                    return dict(zip(columns, row))
                
                return None
                
        except Exception as e:
            logger.error(f"Erreur lors de la recherche client: {e}")
            return None
    
    def get_client_contracts(self, client_id: int) -> List[Dict]:
        """Récupère tous les contrats d'un client"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT c.*, p.nom as produit_nom, p.branche, p.description
                    FROM contrats c
                    JOIN produits p ON c.produit_id = p.produit_id
                    WHERE c.client_id = ?
                    ORDER BY c.date_souscription DESC
                ''', (client_id,))
                
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des contrats: {e}")
            return []
    
    def get_contract_guarantees(self, contrat_id: int) -> List[Dict]:
        """Récupère les garanties d'un contrat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM garanties WHERE contrat_id = ?
                ''', (contrat_id,))
                
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des garanties: {e}")
            return []
    
    def get_client_claims(self, client_id: int) -> List[Dict]:
        """Récupère tous les sinistres d'un client"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT s.*, c.numero_contrat, p.nom as produit_nom
                    FROM sinistres s
                    JOIN contrats c ON s.contrat_id = c.contrat_id
                    JOIN produits p ON c.produit_id = p.produit_id
                    WHERE c.client_id = ?
                    ORDER BY s.date_sinistre DESC
                ''', (client_id,))
                
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des sinistres: {e}")
            return []
    
    def get_payment_status(self, client_id: int) -> List[Dict]:
        """Récupère le statut des paiements d'un client"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT p.*, c.numero_contrat, pr.nom as produit_nom
                    FROM paiements p
                    JOIN contrats c ON p.contrat_id = c.contrat_id
                    JOIN produits pr ON c.produit_id = pr.produit_id
                    WHERE c.client_id = ?
                    ORDER BY p.date_echeance DESC
                ''', (client_id,))
                
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des paiements: {e}")
            return []
    
    def check_claim_coverage(self, sinistre_id: int) -> Dict:
        """Vérifie si un sinistre est couvert"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT s.*, c.statut as contrat_statut, c.date_debut, c.date_fin,
                           g.nom_garantie, g.montant_couvert, g.franchise, g.conditions
                    FROM sinistres s
                    JOIN contrats c ON s.contrat_id = c.contrat_id
                    LEFT JOIN garanties g ON c.contrat_id = g.contrat_id
                    WHERE s.sinistre_id = ?
                ''', (sinistre_id,))
                
                rows = cursor.fetchall()
                if not rows:
                    return {"couvert": False, "raison": "Sinistre non trouvé"}
                
                # Analyser la couverture
                sinistre = rows[0]
                date_sinistre = datetime.strptime(sinistre[2], "%Y-%m-%d").date()
                date_debut = datetime.strptime(sinistre[7], "%Y-%m-%d").date()
                date_fin = datetime.strptime(sinistre[8], "%Y-%m-%d").date()
                
                # Vérifications
                if sinistre[6] != "actif":
                    return {"couvert": False, "raison": "Contrat non actif"}
                
                if not (date_debut <= date_sinistre <= date_fin):
                    return {"couvert": False, "raison": "Sinistre hors période de couverture"}
                
                # Vérifier les garanties applicables
                garanties_applicables = []
                for row in rows:
                    if row[9]:  # nom_garantie existe
                        garanties_applicables.append({
                            "garantie": row[9],
                            "montant_couvert": row[10],
                            "franchise": row[11],
                            "conditions": row[12]
                        })
                
                return {
                    "couvert": True,
                    "garanties": garanties_applicables,
                    "montant_dommage": sinistre[4],
                    "montant_indemnise": sinistre[5]
                }
                
        except Exception as e:
            logger.error(f"Erreur lors de la vérification de couverture: {e}")
            return {"couvert": False, "raison": "Erreur technique"}

# Instance globale
db_manager = DatabaseManager()
