"""
Générateur de devis pour BH Assurance
Collecte les informations et génère des devis personnalisés
"""

import logging
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime, date
import re
import random

logger = logging.getLogger(__name__)

class QuoteGenerator:
    def __init__(self):
        self.quote_sessions = {}  # Stockage temporaire des sessions de devis
        
        # Tarifs de base par produit
        self.base_rates = {
            "auto": {
                "essentielle": 200,
                "confort": 400,
                "tous_risques": 800
            },
            "habitation": {
                "standard": 150,
                "confort": 250,
                "premium": 400
            },
            "sante": {
                "individuelle": 300,
                "familiale": 600,
                "premium": 900
            },
            "vie": {
                "temporaire": 500,
                "permanente": 800,
                "mixte": 1200
            }
        }
        
        # Facteurs de risque
        self.risk_factors = {
            "auto": {
                "age_conducteur": {
                    "18-25": 1.5,
                    "26-35": 1.2,
                    "36-50": 1.0,
                    "51-65": 1.1,
                    "65+": 1.3
                },
                "experience": {
                    "0-2": 1.4,
                    "3-5": 1.2,
                    "6-10": 1.0,
                    "10+": 0.9
                },
                "zone": {
                    "tunis": 1.2,
                    "sfax": 1.1,
                    "sousse": 1.0,
                    "autres": 0.9
                }
            },
            "habitation": {
                "type_logement": {
                    "appartement": 1.0,
                    "villa": 1.3,
                    "maison": 1.1
                },
                "zone": {
                    "centre_ville": 1.2,
                    "banlieue": 1.0,
                    "rural": 0.8
                },
                "surface": {
                    "0-100": 1.0,
                    "100-200": 1.2,
                    "200+": 1.5
                }
            }
        }
    
    def start_quote_session(self, session_id: str, insurance_type: str) -> Dict:
        """Démarre une session de devis"""
        try:
            self.quote_sessions[session_id] = {
                "insurance_type": insurance_type,
                "collected_info": {},
                "current_step": 0,
                "questions_asked": [],
                "created_at": datetime.now().isoformat()
            }
            
            # Retourner la première question
            return self._get_next_question(session_id)
            
        except Exception as e:
            logger.error(f"Erreur lors du démarrage de session devis: {e}")
            return {"error": "Erreur lors du démarrage du devis"}
    
    def process_quote_response(self, session_id: str, user_response: str) -> Dict:
        """Traite une réponse utilisateur dans le processus de devis"""
        try:
            if session_id not in self.quote_sessions:
                return {"error": "Session de devis non trouvée"}
            
            session = self.quote_sessions[session_id]
            insurance_type = session["insurance_type"]
            current_step = session["current_step"]
            
            # Extraire et valider l'information
            extracted_info = self._extract_information(user_response, insurance_type, current_step)
            
            if extracted_info:
                # Sauvegarder l'information
                session["collected_info"].update(extracted_info)
                session["current_step"] += 1
                
                # Vérifier si on a toutes les informations nécessaires
                if self._is_quote_complete(session):
                    return self._generate_final_quote(session_id)
                else:
                    return self._get_next_question(session_id)
            else:
                return {
                    "status": "clarification_needed",
                    "message": "Je n'ai pas bien compris votre réponse. Pouvez-vous préciser ?",
                    "current_question": self._get_current_question(session_id)
                }
                
        except Exception as e:
            logger.error(f"Erreur lors du traitement de réponse devis: {e}")
            return {"error": "Erreur lors du traitement de votre réponse"}
    
    def _get_questions_for_type(self, insurance_type: str) -> List[Dict]:
        """Retourne les questions nécessaires pour chaque type d'assurance"""
        questions = {
            "auto": [
                {
                    "key": "age_conducteur",
                    "question": "Quel est votre âge ?",
                    "type": "number",
                    "validation": lambda x: 18 <= int(x) <= 99
                },
                {
                    "key": "experience_conduite",
                    "question": "Depuis combien d'années avez-vous votre permis de conduire ?",
                    "type": "number",
                    "validation": lambda x: 0 <= int(x) <= 50
                },
                {
                    "key": "marque_vehicule",
                    "question": "Quelle est la marque de votre véhicule ?",
                    "type": "text",
                    "validation": lambda x: len(x.strip()) > 0
                },
                {
                    "key": "modele_vehicule",
                    "question": "Quel est le modèle de votre véhicule ?",
                    "type": "text",
                    "validation": lambda x: len(x.strip()) > 0
                },
                {
                    "key": "annee_vehicule",
                    "question": "Quelle est l'année de votre véhicule ?",
                    "type": "number",
                    "validation": lambda x: 1990 <= int(x) <= datetime.now().year + 1
                },
                {
                    "key": "zone_residence",
                    "question": "Dans quelle ville résidez-vous ? (Tunis, Sfax, Sousse, Autre)",
                    "type": "text",
                    "validation": lambda x: len(x.strip()) > 0
                },
                {
                    "key": "formule_souhaitee",
                    "question": "Quelle formule souhaitez-vous ? (Essentielle, Confort, Tous Risques)",
                    "type": "choice",
                    "choices": ["essentielle", "confort", "tous_risques"]
                }
            ],
            "habitation": [
                {
                    "key": "type_logement",
                    "question": "Quel type de logement souhaitez-vous assurer ? (Appartement, Villa, Maison)",
                    "type": "choice",
                    "choices": ["appartement", "villa", "maison"]
                },
                {
                    "key": "surface_logement",
                    "question": "Quelle est la surface de votre logement en m² ?",
                    "type": "number",
                    "validation": lambda x: 20 <= int(x) <= 1000
                },
                {
                    "key": "zone_logement",
                    "question": "Votre logement se trouve-t-il en centre-ville, banlieue ou zone rurale ?",
                    "type": "choice",
                    "choices": ["centre_ville", "banlieue", "rural"]
                },
                {
                    "key": "valeur_mobilier",
                    "question": "Quelle est la valeur estimée de votre mobilier en DT ?",
                    "type": "number",
                    "validation": lambda x: 1000 <= int(x) <= 500000
                },
                {
                    "key": "systeme_securite",
                    "question": "Avez-vous un système de sécurité (alarme, serrures renforcées) ? (Oui/Non)",
                    "type": "boolean",
                    "validation": lambda x: x.lower() in ["oui", "non", "yes", "no"]
                }
            ],
            "sante": [
                {
                    "key": "age_assure",
                    "question": "Quel est votre âge ?",
                    "type": "number",
                    "validation": lambda x: 0 <= int(x) <= 99
                },
                {
                    "key": "situation_familiale",
                    "question": "Quelle est votre situation familiale ? (Célibataire, Marié, Famille)",
                    "type": "choice",
                    "choices": ["celibataire", "marie", "famille"]
                },
                {
                    "key": "nombre_enfants",
                    "question": "Combien d'enfants souhaitez-vous inclure dans l'assurance ?",
                    "type": "number",
                    "validation": lambda x: 0 <= int(x) <= 10
                },
                {
                    "key": "antecedents_medicaux",
                    "question": "Avez-vous des antécédents médicaux particuliers ? (Oui/Non)",
                    "type": "boolean",
                    "validation": lambda x: x.lower() in ["oui", "non", "yes", "no"]
                },
                {
                    "key": "formule_souhaitee",
                    "question": "Quelle formule souhaitez-vous ? (Individuelle, Familiale, Premium)",
                    "type": "choice",
                    "choices": ["individuelle", "familiale", "premium"]
                }
            ]
        }
        
        return questions.get(insurance_type, [])
    
    def _get_next_question(self, session_id: str) -> Dict:
        """Retourne la prochaine question à poser"""
        session = self.quote_sessions[session_id]
        insurance_type = session["insurance_type"]
        current_step = session["current_step"]
        
        questions = self._get_questions_for_type(insurance_type)
        
        if current_step < len(questions):
            question_data = questions[current_step]
            
            response = {
                "status": "question",
                "question": question_data["question"],
                "step": current_step + 1,
                "total_steps": len(questions),
                "insurance_type": insurance_type
            }
            
            if question_data["type"] == "choice":
                response["choices"] = question_data["choices"]
            
            return response
        else:
            return self._generate_final_quote(session_id)
    
    def _get_current_question(self, session_id: str) -> str:
        """Retourne la question actuelle"""
        session = self.quote_sessions[session_id]
        insurance_type = session["insurance_type"]
        current_step = session["current_step"]
        
        questions = self._get_questions_for_type(insurance_type)
        
        if current_step < len(questions):
            return questions[current_step]["question"]
        
        return "Toutes les informations ont été collectées."
    
    def _extract_information(self, user_response: str, insurance_type: str, step: int) -> Optional[Dict]:
        """Extrait l'information de la réponse utilisateur"""
        questions = self._get_questions_for_type(insurance_type)
        
        if step >= len(questions):
            return None
        
        question_data = questions[step]
        key = question_data["key"]
        question_type = question_data["type"]
        
        try:
            if question_type == "number":
                # Extraire le nombre
                numbers = re.findall(r'\d+', user_response)
                if numbers:
                    value = int(numbers[0])
                    if question_data["validation"](value):
                        return {key: value}
            
            elif question_type == "text":
                value = user_response.strip()
                if question_data["validation"](value):
                    return {key: value}
            
            elif question_type == "choice":
                user_lower = user_response.lower()
                for choice in question_data["choices"]:
                    if choice.lower() in user_lower:
                        return {key: choice}
            
            elif question_type == "boolean":
                user_lower = user_response.lower()
                if any(word in user_lower for word in ["oui", "yes", "vrai", "true"]):
                    return {key: True}
                elif any(word in user_lower for word in ["non", "no", "faux", "false"]):
                    return {key: False}
            
            return None
            
        except Exception as e:
            logger.error(f"Erreur lors de l'extraction d'information: {e}")
            return None
    
    def _is_quote_complete(self, session: Dict) -> bool:
        """Vérifie si toutes les informations nécessaires ont été collectées"""
        insurance_type = session["insurance_type"]
        collected_info = session["collected_info"]
        
        questions = self._get_questions_for_type(insurance_type)
        required_keys = [q["key"] for q in questions]
        
        return all(key in collected_info for key in required_keys)
    
    def _generate_final_quote(self, session_id: str) -> Dict:
        """Génère le devis final"""
        try:
            session = self.quote_sessions[session_id]
            insurance_type = session["insurance_type"]
            info = session["collected_info"]
            
            # Calculer le tarif de base
            if insurance_type == "auto":
                base_rate = self.base_rates["auto"][info["formule_souhaitee"]]
                
                # Appliquer les facteurs de risque
                age_factor = self._get_age_factor(info["age_conducteur"])
                exp_factor = self._get_experience_factor(info["experience_conduite"])
                zone_factor = self._get_zone_factor(info["zone_residence"])
                
                final_rate = base_rate * age_factor * exp_factor * zone_factor
                
            elif insurance_type == "habitation":
                base_rate = self.base_rates["habitation"]["standard"]  # Formule par défaut
                
                # Facteurs de risque
                type_factor = self.risk_factors["habitation"]["type_logement"].get(info["type_logement"], 1.0)
                zone_factor = self.risk_factors["habitation"]["zone"].get(info["zone_logement"], 1.0)
                surface_factor = self._get_surface_factor(info["surface_logement"])
                security_factor = 0.9 if info.get("systeme_securite", False) else 1.0
                
                final_rate = base_rate * type_factor * zone_factor * surface_factor * security_factor
                
            elif insurance_type == "sante":
                formule = info.get("formule_souhaitee", "individuelle")
                base_rate = self.base_rates["sante"][formule]
                
                # Facteurs d'âge et famille
                age_factor = 1.0 + (info["age_assure"] - 30) * 0.01 if info["age_assure"] > 30 else 1.0
                family_factor = 1.0 + info.get("nombre_enfants", 0) * 0.2
                medical_factor = 1.3 if info.get("antecedents_medicaux", False) else 1.0
                
                final_rate = base_rate * age_factor * family_factor * medical_factor
            
            else:
                final_rate = 500  # Tarif par défaut
            
            # Générer le devis
            quote = {
                "status": "quote_generated",
                "quote_id": f"DEVIS_{session_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                "insurance_type": insurance_type,
                "client_info": info,
                "pricing": {
                    "prime_annuelle": round(final_rate, 2),
                    "prime_mensuelle": round(final_rate / 12, 2),
                    "franchise": self._calculate_franchise(insurance_type, final_rate),
                    "plafond_garantie": self._calculate_coverage_limit(insurance_type, final_rate)
                },
                "guarantees": self._get_guarantees_for_type(insurance_type),
                "validity": "30 jours",
                "generated_at": datetime.now().isoformat()
            }
            
            # Nettoyer la session
            del self.quote_sessions[session_id]
            
            return quote
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération du devis: {e}")
            return {"error": "Erreur lors de la génération du devis"}
    
    def _get_age_factor(self, age: int) -> float:
        """Calcule le facteur d'âge pour l'assurance auto"""
        if 18 <= age <= 25:
            return 1.5
        elif 26 <= age <= 35:
            return 1.2
        elif 36 <= age <= 50:
            return 1.0
        elif 51 <= age <= 65:
            return 1.1
        else:
            return 1.3
    
    def _get_experience_factor(self, experience: int) -> float:
        """Calcule le facteur d'expérience pour l'assurance auto"""
        if experience <= 2:
            return 1.4
        elif experience <= 5:
            return 1.2
        elif experience <= 10:
            return 1.0
        else:
            return 0.9
    
    def _get_zone_factor(self, zone: str) -> float:
        """Calcule le facteur de zone"""
        zone_lower = zone.lower()
        if "tunis" in zone_lower:
            return 1.2
        elif "sfax" in zone_lower:
            return 1.1
        elif "sousse" in zone_lower:
            return 1.0
        else:
            return 0.9
    
    def _get_surface_factor(self, surface: int) -> float:
        """Calcule le facteur de surface pour l'habitation"""
        if surface <= 100:
            return 1.0
        elif surface <= 200:
            return 1.2
        else:
            return 1.5
    
    def _calculate_franchise(self, insurance_type: str, premium: float) -> float:
        """Calcule la franchise"""
        if insurance_type == "auto":
            return max(200, premium * 0.1)
        elif insurance_type == "habitation":
            return max(150, premium * 0.08)
        else:
            return max(50, premium * 0.05)
    
    def _calculate_coverage_limit(self, insurance_type: str, premium: float) -> float:
        """Calcule le plafond de garantie"""
        if insurance_type == "auto":
            return premium * 100
        elif insurance_type == "habitation":
            return premium * 200
        elif insurance_type == "sante":
            return premium * 300
        else:
            return premium * 150
    
    def _get_guarantees_for_type(self, insurance_type: str) -> List[str]:
        """Retourne les garanties pour chaque type d'assurance"""
        guarantees = {
            "auto": [
                "Responsabilité civile",
                "Protection juridique",
                "Assistance 24h/24",
                "Véhicule de remplacement"
            ],
            "habitation": [
                "Incendie et explosion",
                "Vol et tentative de vol",
                "Dégâts des eaux",
                "Responsabilité civile",
                "Assistance dépannage"
            ],
            "sante": [
                "Hospitalisation",
                "Soins ambulatoires",
                "Pharmacie",
                "Analyses et radiologies"
            ],
            "vie": [
                "Capital décès",
                "Rente de survie",
                "Exonération des primes"
            ]
        }
        
        return guarantees.get(insurance_type, [])

# Instance globale
quote_generator = QuoteGenerator()
