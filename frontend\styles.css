/* Variables CSS */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--surface-color);
    box-shadow: var(--shadow-lg);
}

/* Header */
.header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.header-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--background-color);
}

/* Welcome Section */
.welcome-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
}

.welcome-content {
    max-width: 600px;
}

.welcome-icon {
    margin-bottom: 1.5rem;
}

.welcome-icon i {
    font-size: 4rem;
    color: var(--primary-color);
}

.welcome-content h2 {
    font-size: 1.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.welcome-content p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.quick-actions h3 {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.quick-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.quick-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.quick-btn i {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.quick-btn:hover i {
    color: white;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 2rem;
    display: none;
}

.chat-messages.active {
    display: block;
}

.message {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--primary-color);
    color: white;
}

.message.bot .message-avatar {
    background: var(--secondary-color);
    color: white;
}

.message-content {
    max-width: 70%;
    padding: 1rem 1.25rem;
    border-radius: var(--border-radius);
    position: relative;
}

.message.user .message-content {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: 4px;
}

.message.bot .message-content {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 4px;
    box-shadow: var(--shadow-sm);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    text-align: right;
}

.message.bot .message-time {
    text-align: left;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--text-secondary);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Input Section */
.input-section {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
}

.input-container {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

#messageInput {
    flex: 1;
    padding: 0.875rem 1.25rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--background-color);
    transition: var(--transition);
}

#messageInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

#sendButton {
    padding: 0.875rem 1.25rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 1.125rem;
}

#sendButton:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

#sendButton:disabled {
    background: var(--secondary-color);
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: var(--surface-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--error-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

.btn-primary {
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary:hover {
    background: var(--primary-dark);
}

/* Client Login Modal */
.client-login-form {
    display: flex;
    gap: 0.75rem;
    margin: 1rem 0;
}

.client-login-form input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.client-login-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.client-examples {
    background: var(--background-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

.client-examples ul {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0 0 0;
}

.client-examples li {
    padding: 0.25rem 0;
    color: var(--text-secondary);
    font-family: monospace;
    font-size: 0.875rem;
}

.client-examples li:hover {
    color: var(--primary-color);
    cursor: pointer;
}

/* Enhanced message styles for structured data */
.message-content.structured {
    max-width: 85%;
}

.message-content .quote-card,
.message-content .client-card,
.message-content .contract-card {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 0.5rem 0;
}

.message-content .quote-header,
.message-content .client-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.message-content .quote-pricing,
.message-content .client-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
    margin: 1rem 0;
}

.message-content .pricing-item,
.message-content .summary-item {
    text-align: center;
    padding: 0.75rem;
    background: var(--surface-color);
    border-radius: calc(var(--border-radius) / 2);
    border: 1px solid var(--border-color);
}

.message-content .pricing-value,
.message-content .summary-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.message-content .pricing-label,
.message-content .summary-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.message-content .guarantees-list,
.message-content .contracts-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.message-content .guarantees-list li,
.message-content .contracts-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.message-content .guarantees-list li:last-child,
.message-content .contracts-list li:last-child {
    border-bottom: none;
}

.message-content .status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: calc(var(--border-radius) / 2);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.message-content .status-badge.active {
    background: var(--success-color);
    color: white;
}

.message-content .status-badge.pending {
    background: var(--warning-color);
    color: white;
}

.message-content .status-badge.overdue {
    background: var(--error-color);
    color: white;
}

/* Client button styling */
.quick-btn.client-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-color: var(--primary-color);
}

.quick-btn.client-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
}

.quick-btn.client-btn i {
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        height: 100vh;
        border-radius: 0;
    }
    
    .header {
        padding: 1rem;
    }
    
    .logo h1 {
        font-size: 1.25rem;
    }
    
    .welcome-content {
        padding: 1rem;
    }
    
    .quick-buttons {
        grid-template-columns: 1fr;
    }
    
    .chat-messages {
        padding: 1rem;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .input-section {
        padding: 1rem;
    }
}
