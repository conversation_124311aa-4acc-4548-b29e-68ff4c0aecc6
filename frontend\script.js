// Configuration
const API_BASE_URL = 'http://127.0.0.1:5000';
let sessionId = null;
let isLoading = false;

// Éléments DOM
const welcomeSection = document.getElementById('welcomeSection');
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const typingIndicator = document.getElementById('typingIndicator');
const charCounter = document.getElementById('charCounter');
const loadingOverlay = document.getElementById('loadingOverlay');
const errorModal = document.getElementById('errorModal');
const errorMessage = document.getElementById('errorMessage');

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
});

function initializeApp() {
    // Générer un ID de session unique
    sessionId = generateSessionId();
    
    // Vérifier la connexion au serveur
    checkServerConnection();
    
    // Focus sur l'input
    messageInput.focus();
}

function setupEventListeners() {
    // Input message
    messageInput.addEventListener('input', function() {
        updateCharCounter();
        toggleSendButton();
    });
    
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // Send button
    sendButton.addEventListener('click', sendMessage);
    
    // Fermeture du modal d'erreur avec Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

function generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

async function checkServerConnection() {
    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/health`);
        const data = await response.json();
        
        if (data.status === 'healthy') {
            console.log('✅ Connexion au serveur établie');
        } else {
            throw new Error('Serveur non disponible');
        }
    } catch (error) {
        console.error('❌ Erreur de connexion:', error);
        showError('Impossible de se connecter au serveur. Veuillez vérifier que le serveur est démarré.');
    } finally {
        showLoading(false);
    }
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showError(message) {
    errorMessage.textContent = message;
    errorModal.classList.add('active');
}

function closeModal() {
    errorModal.classList.remove('active');
}

function updateCharCounter() {
    const length = messageInput.value.length;
    charCounter.textContent = `${length}/500`;
    
    if (length > 450) {
        charCounter.style.color = 'var(--warning-color)';
    } else if (length > 480) {
        charCounter.style.color = 'var(--error-color)';
    } else {
        charCounter.style.color = 'var(--text-secondary)';
    }
}

function toggleSendButton() {
    const hasText = messageInput.value.trim().length > 0;
    sendButton.disabled = !hasText || isLoading;
}

function sendQuickMessage(message) {
    messageInput.value = message;
    sendMessage();
}

async function sendMessage() {
    const message = messageInput.value.trim();
    
    if (!message || isLoading) return;
    
    // Masquer la section de bienvenue et afficher le chat
    if (welcomeSection.style.display !== 'none') {
        welcomeSection.style.display = 'none';
        chatMessages.classList.add('active');
    }
    
    // Ajouter le message de l'utilisateur
    addMessage(message, 'user');
    
    // Vider l'input et désactiver le bouton
    messageInput.value = '';
    updateCharCounter();
    toggleSendButton();
    
    // Afficher l'indicateur de frappe
    showTypingIndicator(true);
    
    isLoading = true;
    
    try {
        const response = await fetch(`${API_BASE_URL}/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                session_id: sessionId
            })
        });
        
        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Masquer l'indicateur de frappe
        showTypingIndicator(false);
        
        // Ajouter la réponse du bot
        addMessage(data.response, 'bot');
        
    } catch (error) {
        console.error('Erreur lors de l\'envoi du message:', error);
        showTypingIndicator(false);
        
        addMessage(
            'Désolé, je rencontre une difficulté technique. Veuillez réessayer ou contacter notre service client au +216 XX XXX XXX.',
            'bot',
            true
        );
    } finally {
        isLoading = false;
        toggleSendButton();
        messageInput.focus();
    }
}

function addMessage(content, sender, isError = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    if (isError) {
        messageContent.style.borderColor = 'var(--error-color)';
        messageContent.style.backgroundColor = '#fef2f2';
    }
    
    const messageText = document.createElement('div');
    messageText.textContent = content;
    
    const messageTime = document.createElement('div');
    messageTime.className = 'message-time';
    messageTime.textContent = new Date().toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    messageContent.appendChild(messageText);
    messageContent.appendChild(messageTime);
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);
    
    chatMessages.appendChild(messageDiv);
    
    // Scroll vers le bas
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Animation d'apparition
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(20px)';
    
    requestAnimationFrame(() => {
        messageDiv.style.transition = 'all 0.3s ease';
        messageDiv.style.opacity = '1';
        messageDiv.style.transform = 'translateY(0)';
    });
}

function showTypingIndicator(show) {
    typingIndicator.style.display = show ? 'flex' : 'none';
    
    if (show) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Fonctions utilitaires
function formatTime(date) {
    return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

function sanitizeHTML(str) {
    const temp = document.createElement('div');
    temp.textContent = str;
    return temp.innerHTML;
}

// Gestion des erreurs globales
window.addEventListener('error', function(e) {
    console.error('Erreur JavaScript:', e.error);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('Promise rejetée:', e.reason);
});

// Export pour les tests (si nécessaire)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateSessionId,
        sanitizeHTML,
        formatTime
    };
}
