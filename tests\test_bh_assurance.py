"""
Tests unitaires pour l'agent conversationnel BH Assurance
"""

import unittest
import json
import tempfile
import os
from unittest.mock import Mock, patch
import sys
sys.path.append('../backend')

from backend.prompts import BHAssurancePrompts
from backend.document_manager import DocumentManager
from backend.conversation_manager import ConversationManager
from backend.database_manager import DatabaseManager
from backend.client_analyzer import ClientAnalyzer
from backend.quote_generator import QuoteGenerator

class TestBHAssurancePrompts(unittest.TestCase):
    def setUp(self):
        self.prompts = BHAssurancePrompts()
    
    def test_detect_intent_auto(self):
        """Test de détection d'intention pour l'assurance auto"""
        message = "Je veux souscrire une assurance pour ma voiture"
        intent = self.prompts.detect_intent(message)
        self.assertEqual(intent, "auto")
    
    def test_detect_intent_sante(self):
        """Test de détection d'intention pour l'assurance santé"""
        message = "Quels sont les remboursements médicaux ?"
        intent = self.prompts.detect_intent(message)
        self.assertEqual(intent, "sante")
    
    def test_detect_intent_habitation(self):
        """Test de détection d'intention pour l'assurance habitation"""
        message = "Mon appartement a été cambriolé"
        intent = self.prompts.detect_intent(message)
        self.assertEqual(intent, "habitation")
    
    def test_detect_intent_reclamation(self):
        """Test de détection d'intention pour les réclamations"""
        message = "Comment faire une réclamation ?"
        intent = self.prompts.detect_intent(message)
        self.assertEqual(intent, "reclamation")
    
    def test_detect_intent_general(self):
        """Test de détection d'intention générale"""
        message = "Bonjour, comment allez-vous ?"
        intent = self.prompts.detect_intent(message)
        self.assertEqual(intent, "general")
    
    def test_get_specialized_prompt(self):
        """Test de génération de prompt spécialisé"""
        message = "Combien coûte une assurance auto ?"
        history = []
        prompt = self.prompts.get_specialized_prompt(message, history)
        
        self.assertIn("BH Assurance", prompt)
        self.assertIn("assurance automobile", prompt.lower())
        self.assertIn(message, prompt)
    
    def test_format_history(self):
        """Test de formatage de l'historique"""
        history = [
            {"user": "Bonjour", "bot": "Bonjour, comment puis-je vous aider ?"},
            {"user": "Tarifs auto", "bot": "Nos tarifs commencent à 200 DT/an"}
        ]
        
        formatted = self.prompts._format_history(history)
        self.assertIn("Client: Bonjour", formatted)
        self.assertIn("Assistant:", formatted)

class TestDocumentManager(unittest.TestCase):
    def setUp(self):
        # Créer un répertoire temporaire pour les tests
        self.temp_dir = tempfile.mkdtemp()
        self.doc_manager = DocumentManager(self.temp_dir)
    
    def tearDown(self):
        # Nettoyer le répertoire temporaire
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_search_knowledge_auto(self):
        """Test de recherche dans la base de connaissances - auto"""
        results = self.doc_manager.search_knowledge("assurance voiture")
        
        self.assertGreater(len(results), 0)
        self.assertEqual(results[0]["category"], "assurance_auto")
        self.assertIn("couvertures", results[0]["content"])
    
    def test_search_knowledge_sante(self):
        """Test de recherche dans la base de connaissances - santé"""
        results = self.doc_manager.search_knowledge("remboursement médical")
        
        self.assertGreater(len(results), 0)
        found_sante = any(r["category"] == "assurance_sante" for r in results)
        self.assertTrue(found_sante)
    
    def test_search_knowledge_contact(self):
        """Test de recherche d'informations de contact"""
        results = self.doc_manager.search_knowledge("téléphone contact")
        
        self.assertGreater(len(results), 0)
        found_contact = any(r["category"] == "contact_info" for r in results)
        self.assertTrue(found_contact)
    
    def test_get_contextual_info(self):
        """Test de récupération d'informations contextuelles"""
        context = self.doc_manager.get_contextual_info("assurance auto tarifs")
        
        self.assertIsInstance(context, str)
        self.assertIn("ASSURANCE", context.upper())
    
    def test_add_document(self):
        """Test d'ajout de document"""
        content = "Ceci est un document de test"
        doc_id = self.doc_manager.add_document("test.txt", content, "test")
        
        self.assertIsNotNone(doc_id)
        self.assertIsInstance(doc_id, str)

class TestConversationManager(unittest.TestCase):
    def setUp(self):
        # Créer une base de données temporaire pour les tests
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.conv_manager = ConversationManager(self.temp_db.name)
    
    def tearDown(self):
        # Nettoyer la base de données temporaire
        os.unlink(self.temp_db.name)
    
    def test_create_session(self):
        """Test de création de session"""
        session_id = "test_session_123"
        user_info = {"name": "Test User", "email": "<EMAIL>"}
        
        result = self.conv_manager.create_session(session_id, user_info)
        self.assertTrue(result)
        
        # Vérifier que la session existe
        session_info = self.conv_manager.get_session_info(session_id)
        self.assertIsNotNone(session_info)
        self.assertEqual(session_info["session_id"], session_id)
    
    def test_add_message(self):
        """Test d'ajout de message"""
        session_id = "test_session_456"
        
        # Ajouter un message utilisateur
        result1 = self.conv_manager.add_message(session_id, "user", "Bonjour")
        self.assertTrue(result1)
        
        # Ajouter une réponse bot
        result2 = self.conv_manager.add_message(session_id, "bot", "Bonjour ! Comment puis-je vous aider ?")
        self.assertTrue(result2)
        
        # Vérifier l'historique
        history = self.conv_manager.get_conversation_history(session_id)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]["type"], "user")
        self.assertEqual(history[1]["type"], "bot")
    
    def test_get_conversation_history(self):
        """Test de récupération d'historique"""
        session_id = "test_session_789"
        
        # Ajouter plusieurs messages
        messages = [
            ("user", "Message 1"),
            ("bot", "Réponse 1"),
            ("user", "Message 2"),
            ("bot", "Réponse 2")
        ]
        
        for msg_type, content in messages:
            self.conv_manager.add_message(session_id, msg_type, content)
        
        # Récupérer l'historique
        history = self.conv_manager.get_conversation_history(session_id)
        self.assertEqual(len(history), 4)
        
        # Vérifier l'ordre chronologique
        self.assertEqual(history[0]["content"], "Message 1")
        self.assertEqual(history[-1]["content"], "Réponse 2")
    
    def test_get_conversation_stats(self):
        """Test de récupération des statistiques"""
        # Créer quelques sessions et messages
        for i in range(3):
            session_id = f"test_session_{i}"
            self.conv_manager.add_message(session_id, "user", f"Message {i}")
            self.conv_manager.add_message(session_id, "bot", f"Réponse {i}")
        
        stats = self.conv_manager.get_conversation_stats()
        
        self.assertIn("total_sessions", stats)
        self.assertIn("total_messages", stats)
        self.assertGreaterEqual(stats["total_sessions"], 3)
        self.assertGreaterEqual(stats["total_messages"], 6)

class TestIntegration(unittest.TestCase):
    """Tests d'intégration pour vérifier que tous les composants fonctionnent ensemble"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.prompts = BHAssurancePrompts()
        self.doc_manager = DocumentManager(self.temp_dir)
        self.conv_manager = ConversationManager(self.temp_db.name)
    
    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir)
        os.unlink(self.temp_db.name)
    
    def test_complete_conversation_flow(self):
        """Test d'un flux de conversation complet"""
        session_id = "integration_test_session"
        user_message = "Je veux une assurance pour ma voiture"
        
        # 1. Détecter l'intention
        intent = self.prompts.detect_intent(user_message)
        self.assertEqual(intent, "auto")
        
        # 2. Récupérer le contexte documentaire
        context = self.doc_manager.get_contextual_info(user_message)
        self.assertIsInstance(context, str)
        
        # 3. Générer le prompt spécialisé
        history = self.conv_manager.get_conversation_history(session_id)
        prompt = self.prompts.get_specialized_prompt(user_message, history)
        self.assertIn("assurance automobile", prompt.lower())
        
        # 4. Sauvegarder la conversation
        self.conv_manager.add_message(session_id, "user", user_message)
        bot_response = "Nos assurances auto commencent à 200 DT/an..."
        self.conv_manager.add_message(session_id, "bot", bot_response)
        
        # 5. Vérifier que tout est sauvegardé
        final_history = self.conv_manager.get_conversation_history(session_id)
        self.assertEqual(len(final_history), 2)
        self.assertEqual(final_history[0]["content"], user_message)
        self.assertEqual(final_history[1]["content"], bot_response)

class TestDatabaseManager(unittest.TestCase):
    """Tests pour le gestionnaire de base de données"""

    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_manager = DatabaseManager(self.temp_db.name)

    def tearDown(self):
        os.unlink(self.temp_db.name)

    def test_get_client_by_identifier(self):
        """Test de recherche de client"""
        # Rechercher par email
        client = self.db_manager.get_client_by_identifier("<EMAIL>")
        self.assertIsNotNone(client)
        self.assertEqual(client['nom'], 'Ben Ali')
        self.assertEqual(client['prenom'], 'Ahmed')

    def test_get_client_contracts(self):
        """Test de récupération des contrats"""
        contracts = self.db_manager.get_client_contracts(1)
        self.assertGreater(len(contracts), 0)
        self.assertIn('numero_contrat', contracts[0])
        self.assertIn('produit_nom', contracts[0])

class TestClientAnalyzer(unittest.TestCase):
    """Tests pour l'analyseur de données clients"""

    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.analyzer = ClientAnalyzer()
        self.analyzer.db = self.db_manager

    def tearDown(self):
        os.unlink(self.temp_db.name)

    def test_identify_client(self):
        """Test d'identification de client"""
        client = self.analyzer.identify_client("Mon <NAME_EMAIL>")
        self.assertIsNotNone(client)
        self.assertEqual(client['email'], '<EMAIL>')

    def test_analyze_client_contracts(self):
        """Test d'analyse des contrats"""
        analysis = self.analyzer.analyze_client_contracts(1)
        self.assertEqual(analysis['status'], 'success')
        self.assertGreater(analysis['total_contracts'], 0)

    def test_analyze_client_claims(self):
        """Test d'analyse des sinistres"""
        analysis = self.analyzer.analyze_client_claims(1)
        # Peut être 'success' ou 'no_claims' selon les données
        self.assertIn(analysis['status'], ['success', 'no_claims'])

class TestQuoteGenerator(unittest.TestCase):
    """Tests pour le générateur de devis"""

    def setUp(self):
        self.quote_gen = QuoteGenerator()

    def test_start_quote_session(self):
        """Test de démarrage de session de devis"""
        session_id = "test_quote_session"
        result = self.quote_gen.start_quote_session(session_id, "auto")

        self.assertEqual(result['status'], 'question')
        self.assertIn('question', result)
        self.assertEqual(result['insurance_type'], 'auto')

    def test_process_quote_response(self):
        """Test de traitement de réponse de devis"""
        session_id = "test_quote_response"

        # Démarrer une session
        self.quote_gen.start_quote_session(session_id, "auto")

        # Répondre à la première question (âge)
        result = self.quote_gen.process_quote_response(session_id, "J'ai 30 ans")

        # Devrait passer à la question suivante
        self.assertIn(result['status'], ['question', 'clarification_needed'])

    def test_complete_quote_flow(self):
        """Test d'un flux complet de devis"""
        session_id = "test_complete_quote"

        # Démarrer
        self.quote_gen.start_quote_session(session_id, "auto")

        # Réponses simulées
        responses = [
            "30 ans",  # âge
            "5 ans",   # expérience
            "Peugeot", # marque
            "208",     # modèle
            "2020",    # année
            "Tunis",   # zone
            "tous risques"  # formule
        ]

        result = None
        for response in responses:
            result = self.quote_gen.process_quote_response(session_id, response)
            if result.get('status') == 'quote_generated':
                break

        # Vérifier que le devis a été généré
        if result and result.get('status') == 'quote_generated':
            self.assertIn('quote_id', result)
            self.assertIn('pricing', result)
            self.assertIn('guarantees', result)

class TestCompleteSystem(unittest.TestCase):
    """Tests d'intégration du système complet"""

    def setUp(self):
        # Créer des instances temporaires
        self.temp_dir = tempfile.mkdtemp()
        self.temp_db_conv = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db_conv.close()
        self.temp_db_client = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db_client.close()

        self.prompts = BHAssurancePrompts()
        self.doc_manager = DocumentManager(self.temp_dir)
        self.conv_manager = ConversationManager(self.temp_db_conv.name)
        self.db_manager = DatabaseManager(self.temp_db_client.name)
        self.client_analyzer = ClientAnalyzer()
        self.client_analyzer.db = self.db_manager
        self.quote_gen = QuoteGenerator()

    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir)
        os.unlink(self.temp_db_conv.name)
        os.unlink(self.temp_db_client.name)

    def test_client_identification_and_analysis(self):
        """Test d'identification et d'analyse client complète"""
        # 1. Identifier un client
        client = self.client_analyzer.identify_client("<EMAIL>")
        self.assertIsNotNone(client)

        # 2. Analyser ses contrats
        contracts = self.client_analyzer.analyze_client_contracts(client['client_id'])
        self.assertEqual(contracts['status'], 'success')

        # 3. Analyser ses sinistres
        claims = self.client_analyzer.analyze_client_claims(client['client_id'])
        self.assertIn(claims['status'], ['success', 'no_claims'])

        # 4. Analyser ses paiements
        payments = self.client_analyzer.analyze_payment_status(client['client_id'])
        self.assertEqual(payments['status'], 'success')

        # 5. Résumé complet
        summary = self.client_analyzer.get_client_summary(client['client_id'])
        self.assertEqual(summary['status'], 'success')
        self.assertIn('client_info', summary)

    def test_quote_and_knowledge_integration(self):
        """Test d'intégration devis et base de connaissances"""
        # 1. Rechercher des informations sur l'assurance auto
        context = self.doc_manager.get_contextual_info("assurance auto tarifs")
        self.assertIsInstance(context, str)
        self.assertIn("AUTO", context.upper())

        # 2. Générer un prompt spécialisé
        prompt = self.prompts.get_specialized_prompt("Je veux un devis auto", [])
        self.assertIn("assurance automobile", prompt.lower())

        # 3. Démarrer un devis
        session_id = "integration_test"
        quote_result = self.quote_gen.start_quote_session(session_id, "auto")
        self.assertEqual(quote_result['status'], 'question')

if __name__ == '__main__':
    # Configuration du logging pour les tests
    import logging
    logging.basicConfig(level=logging.WARNING)

    # Lancer tous les tests
    unittest.main(verbosity=2)
