<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BH Assurance - Assistant Virtuel</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>BH Assurance</h1>
                </div>
                <div class="header-info">
                    <span class="status-indicator online"></span>
                    <span>Assistant disponible 24/7</span>
                </div>
            </div>
        </header>

        <!-- Main Chat Container -->
        <div class="chat-container">
            <!-- Welcome Section -->
            <div class="welcome-section" id="welcomeSection">
                <div class="welcome-content">
                    <div class="welcome-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h2>Bonjour ! Je suis votre assistant BH Assurance</h2>
                    <p>Je suis là pour vous aider avec toutes vos questions sur nos services d'assurance.</p>
                    
                    <div class="quick-actions">
                        <h3>Questions fréquentes :</h3>
                        <div class="quick-buttons">
                            <button class="quick-btn" onclick="sendQuickMessage('Quels sont vos services d\'assurance ?')">
                                <i class="fas fa-list"></i>
                                Nos services
                            </button>
                            <button class="quick-btn" onclick="sendQuickMessage('Comment souscrire une assurance auto ?')">
                                <i class="fas fa-car"></i>
                                Assurance Auto
                            </button>
                            <button class="quick-btn" onclick="sendQuickMessage('Quels documents pour une réclamation ?')">
                                <i class="fas fa-file-alt"></i>
                                Réclamations
                            </button>
                            <button class="quick-btn" onclick="sendQuickMessage('Comment prendre rendez-vous ?')">
                                <i class="fas fa-calendar"></i>
                                Rendez-vous
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Messages -->
            <div class="chat-messages" id="chatMessages">
                <!-- Messages will be added here dynamically -->
            </div>

            <!-- Typing Indicator -->
            <div class="typing-indicator" id="typingIndicator" style="display: none;">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span>L'assistant tape...</span>
            </div>
        </div>

        <!-- Input Section -->
        <div class="input-section">
            <div class="input-container">
                <input 
                    type="text" 
                    id="messageInput" 
                    placeholder="Tapez votre message ici..."
                    maxlength="500"
                >
                <button id="sendButton" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-footer">
                <span class="char-counter" id="charCounter">0/500</span>
                <span class="powered-by">Propulsé par IA BH Assurance</span>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Connexion à l'assistant...</p>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal" id="errorModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Erreur</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p id="errorMessage">Une erreur s'est produite.</p>
            </div>
            <div class="modal-footer">
                <button class="btn-primary" onclick="closeModal()">OK</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
