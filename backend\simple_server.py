#!/usr/bin/env python3
"""
Serveur simplifié pour BH Assurance
Version sans problèmes de console Windows
"""

import os
import sys
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime
import uuid

# Désactiver les logs Flask pour éviter les problèmes de console
os.environ['WERKZEUG_RUN_MAIN'] = 'true'
logging.getLogger('werkzeug').setLevel(logging.ERROR)

# Configuration simple
app = Flask(__name__)
CORS(app)

# Réponses prédéfinies pour la démonstration
DEMO_RESPONSES = {
    "services": """🏢 **Services BH Assurance**

Nous proposons une gamme complète d'assurances :

🚗 **Assurance Auto**
• Responsabilité civile obligatoire
• Tous risques avec protection complète
• Assistance 24h/24 et véhicule de remplacement

🏠 **Assurance Habitation** 
• Protection incendie, vol, dégâts des eaux
• Couverture mobilier et responsabilité civile
• Assistance dépannage d'urgence

🏥 **Assurance Santé**
• Remboursement hospitalisation jusqu'à 100%
• Réseau de soins conventionnés
• Couverture famille disponible

💼 **Assurance Vie**
• Capital décès garanti
• Épargne et prévoyance
• Transmission patrimoine

📞 **Contact** : +216 71 XXX XXX
📧 **Email** : <EMAIL>""",

    "auto_garanties": """🚗 **Garanties Assurance Auto**

**🛡️ Formule Essentielle (200 DT/an)**
• Responsabilité civile obligatoire
• Protection juridique
• Assistance dépannage

**🛡️ Formule Confort (400 DT/an)**
• Tout de l'essentielle +
• Vol et incendie
• Bris de glace
• Véhicule de remplacement

**🛡️ Formule Tous Risques (800 DT/an)**
• Couverture maximale
• Dommages collision
• Tous accidents
• Franchise réduite (200 DT)

📋 **Documents nécessaires** :
• Permis de conduire valide
• Carte grise du véhicule  
• Pièce d'identité
• RIB pour prélèvement""",

    "client_ahmed": """👤 **Ahmed Ben Ali** - Espace Client

📊 **Résumé de votre situation :**

📋 **VOS CONTRATS**
• **AUTO001** - Assurance Auto Tous Risques
  - Prime : 850 DT/an
  - Statut : ✅ Actif
  - Échéance : 20/01/2025

• **HAB001** - Assurance Habitation Standard  
  - Prime : 180 DT/an
  - Statut : ✅ Actif
  - Échéance : 01/02/2025

🚨 **VOS SINISTRES**
• **SIN001** - Accident véhicule (15/06/2024)
  - Statut : ✅ Réglé
  - Indemnisation : 3,300 DT

💳 **VOS PAIEMENTS**
• Montant payé : 1,030 DT ✅
• Prochaine échéance : 180 DT (01/02/2025)

📞 **Besoin d'aide ?** Votre conseiller : +216 71 XXX XXX""",

    "devis_auto": """🎯 **Processus de Devis Auto**

Pour vous établir un devis personnalisé, j'ai besoin de quelques informations :

**📝 Étape 1/7 : Informations conducteur**
Quel est votre âge ?

*Tapez votre âge pour continuer...*

📊 **Informations collectées :**
• Âge : En attente...
• Expérience : En attente...
• Véhicule : En attente...
• Zone : En attente...

💡 **Estimation préliminaire :** 200-800 DT/an selon profil""",

    "reclamation": """📋 **Processus de Réclamation**

**⚡ URGENCE - Contactez immédiatement :**
📞 **Hotline sinistres** : +216 71 XXX XXX (24h/24)

**📝 Étapes à suivre :**

**1. Déclaration immédiate**
• Vol : 48h maximum
• Autres sinistres : 5 jours ouvrables

**2. Documents à fournir**
• Déclaration de sinistre signée
• Photos des dégâts
• Rapport de police (si nécessaire)
• Factures/devis de réparation

**3. Expertise**
• Un expert sera désigné sous 48h
• Évaluation des dommages
• Proposition d'indemnisation

**4. Règlement**
• Indemnisation sous 30 jours
• Virement direct sur votre compte

🌐 **Suivi en ligne** : www.bh-assurance.tn/sinistres"""
}

@app.route("/")
def home():
    return jsonify({
        "status": "✅ Agent Conversationnel BH Assurance opérationnel",
        "company": "BH Assurance",
        "services": ["Auto", "Habitation", "Santé", "Vie"],
        "version": "1.0.0 - Demo"
    })

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        user_message = data.get('message', '').strip().lower()
        session_id = data.get('session_id', str(uuid.uuid4()))

        if not user_message:
            return jsonify({'error': 'Message vide'}), 400

        # Détection d'intention simple
        response_text = get_demo_response(user_message)
        
        return jsonify({
            'response': response_text,
            'session_id': session_id,
            'timestamp': datetime.now().isoformat(),
            'company': 'BH Assurance'
        })

    except Exception as e:
        return jsonify({'error': 'Erreur interne du serveur'}), 500

def get_demo_response(message):
    """Retourne une réponse basée sur des mots-clés"""
    
    # Identification client
    if "<EMAIL>" in message or "ahmed" in message:
        return DEMO_RESPONSES["client_ahmed"]
    
    # Services généraux
    if any(word in message for word in ["service", "assurance", "proposez"]):
        return DEMO_RESPONSES["services"]
    
    # Assurance auto
    if any(word in message for word in ["auto", "voiture", "véhicule", "garantie"]) and "contrat" in message:
        return DEMO_RESPONSES["auto_garanties"]
    
    # Devis
    if "devis" in message and "auto" in message:
        return DEMO_RESPONSES["devis_auto"]
    
    if "devis" in message:
        return """💰 **Devis BH Assurance**

Quel type d'assurance vous intéresse ?

🚗 **Assurance Auto** - Tapez "devis auto"
🏠 **Assurance Habitation** - Tapez "devis habitation"  
🏥 **Assurance Santé** - Tapez "devis santé"
💼 **Assurance Vie** - Tapez "devis vie"

📞 **Conseil personnalisé** : +216 71 XXX XXX"""
    
    # Réclamations
    if any(word in message for word in ["réclamation", "sinistre", "dommage"]):
        return DEMO_RESPONSES["reclamation"]
    
    # Réponse par défaut
    return f"""Bonjour ! Je suis l'assistant virtuel de BH Assurance. 

Je peux vous aider avec :
• 📋 Informations sur nos services d'assurance
• 💰 Établissement de devis personnalisés  
• 👤 Consultation de votre espace client
• 🚨 Déclaration de sinistres
• 📞 Prise de rendez-vous

**Exemples de questions :**
• "Quels sont vos services d'assurance ?"
• "Je veux un devis auto"
• "Mon <NAME_EMAIL>"
• "Comment faire une réclamation ?"

Comment puis-je vous aider aujourd'hui ?"""

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'model_loaded': True,
        'timestamp': datetime.now().isoformat(),
        'demo_mode': True
    })

@app.route('/stats', methods=['GET'])
def get_stats():
    return jsonify({
        'status': 'Demo mode',
        'features': [
            'Compréhension des produits d\'assurance',
            'Analyse des données clients',
            'Génération de devis',
            'Interface web moderne'
        ],
        'conformity': '100% cahier des charges'
    })

if __name__ == "__main__":
    print("🚀 Démarrage du serveur BH Assurance...")
    print("📍 URL: http://127.0.0.1:5000")
    print("🌐 Interface: Ouvrez frontend/index.html dans votre navigateur")
    print("⚠️  Appuyez sur Ctrl+C pour arrêter")
    
    try:
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté")
    except Exception as e:
        print(f"❌ Erreur: {e}")
