"""
Analyseur de données clients pour BH Assurance
Analyse les contrats, sinistres, paiements et couvertures
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, date
import re
from database_manager import db_manager

logger = logging.getLogger(__name__)

class ClientAnalyzer:
    def __init__(self):
        self.db = db_manager
    
    def identify_client(self, user_input: str) -> Optional[Dict]:
        """Identifie un client à partir d'informations fournies"""
        # Extraire les identifiants possibles du message
        identifiers = self._extract_identifiers(user_input)
        
        for identifier in identifiers:
            client = self.db.get_client_by_identifier(identifier)
            if client:
                logger.info(f"Client identifié: {client['nom']} {client['prenom']}")
                return client
        
        return None
    
    def _extract_identifiers(self, text: str) -> List[str]:
        """Extrait les identifiants possibles (email, téléphone, CIN) du texte"""
        identifiers = []
        
        # Email
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        identifiers.extend(emails)
        
        # Téléphone tunisien
        phone_pattern = r'\+216\s*\d{2}\s*\d{3}\s*\d{3}|\d{8}'
        phones = re.findall(phone_pattern, text)
        identifiers.extend(phones)
        
        # CIN (8 chiffres)
        cin_pattern = r'\b\d{8}\b'
        cins = re.findall(cin_pattern, text)
        identifiers.extend(cins)
        
        return identifiers
    
    def analyze_client_contracts(self, client_id: int) -> Dict:
        """Analyse complète des contrats d'un client"""
        try:
            contracts = self.db.get_client_contracts(client_id)
            
            if not contracts:
                return {
                    "status": "no_contracts",
                    "message": "Aucun contrat trouvé pour ce client",
                    "contracts": []
                }
            
            analysis = {
                "status": "success",
                "total_contracts": len(contracts),
                "active_contracts": 0,
                "total_premium": 0,
                "contracts_by_branch": {},
                "contracts_detail": []
            }
            
            for contract in contracts:
                # Statistiques générales
                if contract['statut'] == 'actif':
                    analysis['active_contracts'] += 1
                    analysis['total_premium'] += contract['prime_annuelle']
                
                # Par branche
                branch = contract['branche']
                if branch not in analysis['contracts_by_branch']:
                    analysis['contracts_by_branch'][branch] = 0
                analysis['contracts_by_branch'][branch] += 1
                
                # Détails avec garanties
                guarantees = self.db.get_contract_guarantees(contract['contrat_id'])
                
                contract_detail = {
                    "numero": contract['numero_contrat'],
                    "produit": contract['produit_nom'],
                    "branche": contract['branche'],
                    "formule": contract['formule'],
                    "statut": contract['statut'],
                    "prime_annuelle": contract['prime_annuelle'],
                    "date_debut": contract['date_debut'],
                    "date_fin": contract['date_fin'],
                    "franchise": contract['franchise'],
                    "plafond": contract['plafond_garantie'],
                    "garanties": guarantees
                }
                
                analysis['contracts_detail'].append(contract_detail)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Erreur lors de l'analyse des contrats: {e}")
            return {
                "status": "error",
                "message": "Erreur lors de l'analyse des contrats"
            }
    
    def analyze_client_claims(self, client_id: int) -> Dict:
        """Analyse des sinistres d'un client"""
        try:
            claims = self.db.get_client_claims(client_id)
            
            if not claims:
                return {
                    "status": "no_claims",
                    "message": "Aucun sinistre déclaré pour ce client",
                    "claims": []
                }
            
            analysis = {
                "status": "success",
                "total_claims": len(claims),
                "claims_by_status": {},
                "total_damage": 0,
                "total_indemnified": 0,
                "claims_detail": []
            }
            
            for claim in claims:
                # Statistiques par statut
                status = claim['statut']
                if status not in analysis['claims_by_status']:
                    analysis['claims_by_status'][status] = 0
                analysis['claims_by_status'][status] += 1
                
                # Montants
                if claim['montant_dommage']:
                    analysis['total_damage'] += claim['montant_dommage']
                if claim['montant_indemnise']:
                    analysis['total_indemnified'] += claim['montant_indemnise']
                
                # Vérifier la couverture
                coverage = self.db.check_claim_coverage(claim['sinistre_id'])
                
                claim_detail = {
                    "numero": claim['numero_sinistre'],
                    "contrat": claim['numero_contrat'],
                    "produit": claim['produit_nom'],
                    "type": claim['type_sinistre'],
                    "date_sinistre": claim['date_sinistre'],
                    "date_declaration": claim['date_declaration'],
                    "description": claim['description'],
                    "montant_dommage": claim['montant_dommage'],
                    "montant_indemnise": claim['montant_indemnise'],
                    "statut": claim['statut'],
                    "expert": claim['expert_assigne'],
                    "couverture": coverage
                }
                
                analysis['claims_detail'].append(claim_detail)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Erreur lors de l'analyse des sinistres: {e}")
            return {
                "status": "error",
                "message": "Erreur lors de l'analyse des sinistres"
            }
    
    def analyze_payment_status(self, client_id: int) -> Dict:
        """Analyse du statut des paiements d'un client"""
        try:
            payments = self.db.get_payment_status(client_id)
            
            if not payments:
                return {
                    "status": "no_payments",
                    "message": "Aucun paiement trouvé pour ce client",
                    "payments": []
                }
            
            analysis = {
                "status": "success",
                "total_payments": len(payments),
                "paid_amount": 0,
                "pending_amount": 0,
                "overdue_amount": 0,
                "payments_by_status": {},
                "payments_detail": []
            }
            
            today = date.today()
            
            for payment in payments:
                status = payment['statut']
                amount = payment['montant']
                due_date = datetime.strptime(payment['date_echeance'], "%Y-%m-%d").date()
                
                # Statistiques par statut
                if status not in analysis['payments_by_status']:
                    analysis['payments_by_status'][status] = 0
                analysis['payments_by_status'][status] += 1
                
                # Calcul des montants
                if status == 'paye':
                    analysis['paid_amount'] += amount
                elif status == 'en_attente':
                    if due_date < today:
                        analysis['overdue_amount'] += amount
                    else:
                        analysis['pending_amount'] += amount
                
                # Déterminer le statut réel
                real_status = status
                if status == 'en_attente' and due_date < today:
                    real_status = 'en_retard'
                
                payment_detail = {
                    "contrat": payment['numero_contrat'],
                    "produit": payment['produit_nom'],
                    "montant": payment['montant'],
                    "date_echeance": payment['date_echeance'],
                    "date_paiement": payment['date_paiement'],
                    "statut": real_status,
                    "mode_paiement": payment['mode_paiement'],
                    "jours_retard": (today - due_date).days if due_date < today and status == 'en_attente' else 0
                }
                
                analysis['payments_detail'].append(payment_detail)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Erreur lors de l'analyse des paiements: {e}")
            return {
                "status": "error",
                "message": "Erreur lors de l'analyse des paiements"
            }
    
    def check_specific_claim_coverage(self, client_id: int, claim_identifier: str) -> Dict:
        """Vérifie la couverture d'un sinistre spécifique"""
        try:
            claims = self.db.get_client_claims(client_id)
            
            # Rechercher le sinistre
            target_claim = None
            for claim in claims:
                if (claim_identifier.lower() in claim['numero_sinistre'].lower() or
                    claim_identifier.lower() in claim['description'].lower()):
                    target_claim = claim
                    break
            
            if not target_claim:
                return {
                    "status": "not_found",
                    "message": f"Sinistre '{claim_identifier}' non trouvé"
                }
            
            # Analyser la couverture
            coverage = self.db.check_claim_coverage(target_claim['sinistre_id'])
            
            result = {
                "status": "success",
                "sinistre": {
                    "numero": target_claim['numero_sinistre'],
                    "type": target_claim['type_sinistre'],
                    "date": target_claim['date_sinistre'],
                    "description": target_claim['description'],
                    "montant_dommage": target_claim['montant_dommage'],
                    "statut": target_claim['statut']
                },
                "couverture": coverage
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Erreur lors de la vérification de couverture: {e}")
            return {
                "status": "error",
                "message": "Erreur lors de la vérification de couverture"
            }
    
    def get_client_summary(self, client_id: int) -> Dict:
        """Résumé complet d'un client"""
        try:
            client = self.db.get_client_by_identifier(str(client_id))
            if not client:
                return {"status": "not_found", "message": "Client non trouvé"}
            
            contracts_analysis = self.analyze_client_contracts(client_id)
            claims_analysis = self.analyze_client_claims(client_id)
            payments_analysis = self.analyze_payment_status(client_id)
            
            summary = {
                "status": "success",
                "client_info": {
                    "nom": client['nom'],
                    "prenom": client['prenom'],
                    "email": client['email'],
                    "telephone": client['telephone'],
                    "statut": client['statut']
                },
                "contracts_summary": {
                    "total": contracts_analysis.get('total_contracts', 0),
                    "actifs": contracts_analysis.get('active_contracts', 0),
                    "prime_totale": contracts_analysis.get('total_premium', 0)
                },
                "claims_summary": {
                    "total": claims_analysis.get('total_claims', 0),
                    "montant_total_dommages": claims_analysis.get('total_damage', 0),
                    "montant_indemnise": claims_analysis.get('total_indemnified', 0)
                },
                "payments_summary": {
                    "montant_paye": payments_analysis.get('paid_amount', 0),
                    "montant_en_attente": payments_analysis.get('pending_amount', 0),
                    "montant_en_retard": payments_analysis.get('overdue_amount', 0)
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération du résumé client: {e}")
            return {
                "status": "error",
                "message": "Erreur lors de la génération du résumé"
            }

# Instance globale
client_analyzer = ClientAnalyzer()
