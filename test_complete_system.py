#!/usr/bin/env python3
"""
Test complet du système BH Assurance
Valide toutes les fonctionnalités selon le cahier des charges
"""

import requests
import json
import time
from datetime import datetime

# Configuration
API_BASE_URL = "http://127.0.0.1:5000"
TEST_SESSION_ID = f"test_session_{int(time.time())}"

def print_section(title):
    """Affiche une section de test"""
    print("\n" + "="*60)
    print(f"🧪 {title}")
    print("="*60)

def print_test(test_name, success, details=""):
    """Affiche le résultat d'un test"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"   {details}")

def test_server_health():
    """Test 1: Vérification de l'état du serveur"""
    print_section("TEST 1: SANTÉ DU SERVEUR")
    
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        data = response.json()
        
        print_test("Serveur accessible", response.status_code == 200)
        print_test("Modèle IA chargé", data.get('model_loaded', False))
        print_test("Base de données opérationnelle", 'conversation_stats' in data)
        
        return response.status_code == 200
    except Exception as e:
        print_test("Connexion serveur", False, str(e))
        return False

def test_general_chat():
    """Test 2: Chat général et compréhension des produits"""
    print_section("TEST 2: COMPRÉHENSION DES PRODUITS D'ASSURANCE")
    
    questions = [
        "Quels sont vos services d'assurance ?",
        "Quelles sont les garanties incluses dans le contrat auto ?",
        "Quelle est la différence entre la formule standard et premium ?",
        "Comment faire une réclamation ?"
    ]
    
    success_count = 0
    
    for question in questions:
        try:
            response = requests.post(
                f"{API_BASE_URL}/chat",
                json={
                    "message": question,
                    "session_id": TEST_SESSION_ID + "_general"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                
                # Vérifier que la réponse contient des informations pertinentes
                relevant = any(word in response_text.lower() for word in 
                             ['assurance', 'garantie', 'contrat', 'bh assurance'])
                
                print_test(f"Question: {question[:50]}...", relevant)
                if relevant:
                    success_count += 1
            else:
                print_test(f"Question: {question[:50]}...", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            print_test(f"Question: {question[:50]}...", False, str(e))
    
    print(f"\n📊 Résultat: {success_count}/{len(questions)} questions traitées avec succès")
    return success_count >= len(questions) * 0.8  # 80% de réussite minimum

def test_client_data_analysis():
    """Test 3: Analyse des données clients"""
    print_section("TEST 3: ANALYSE DES DONNÉES CLIENTS")
    
    # Test d'identification de client
    test_clients = [
        "<EMAIL>",
        "<EMAIL>",
        "+216 20 123 456",
        "12345678"
    ]
    
    success_count = 0
    
    for client_id in test_clients:
        try:
            # Test d'identification via chat
            response = requests.post(
                f"{API_BASE_URL}/chat",
                json={
                    "message": f"Mon identifiant est {client_id}",
                    "session_id": TEST_SESSION_ID + f"_client_{client_id.replace('@', '_').replace('+', '_')}"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                
                # Vérifier que le client a été identifié
                identified = any(word in response_text.lower() for word in 
                               ['contrat', 'client', 'bonjour', 'identifié'])
                
                print_test(f"Identification client: {client_id}", identified)
                if identified:
                    success_count += 1
            else:
                print_test(f"Identification client: {client_id}", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            print_test(f"Identification client: {client_id}", False, str(e))
    
    # Test des requêtes spécifiques clients
    client_queries = [
        "Quels sont mes contrats ?",
        "Quel est le statut de mes paiements ?",
        "Ai-je des sinistres en cours ?",
        "Donnez-moi un résumé de ma situation"
    ]
    
    for query in client_queries:
        try:
            response = requests.post(
                f"{API_BASE_URL}/chat",
                json={
                    "message": query,
                    "session_id": TEST_SESSION_ID + "<EMAIL>"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                
                # Vérifier que la réponse contient des données client
                has_data = any(word in response_text.lower() for word in 
                             ['contrat', 'paiement', 'sinistre', 'dt', 'statut'])
                
                print_test(f"Requête client: {query}", has_data)
                if has_data:
                    success_count += 1
            else:
                print_test(f"Requête client: {query}", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            print_test(f"Requête client: {query}", False, str(e))
    
    total_tests = len(test_clients) + len(client_queries)
    print(f"\n📊 Résultat: {success_count}/{total_tests} tests clients réussis")
    return success_count >= total_tests * 0.7  # 70% de réussite minimum

def test_quote_generation():
    """Test 4: Génération de devis via API"""
    print_section("TEST 4: GÉNÉRATION DE DEVIS")
    
    insurance_types = ["auto", "habitation", "sante"]
    success_count = 0
    
    for insurance_type in insurance_types:
        try:
            # Démarrer un devis via chat
            response = requests.post(
                f"{API_BASE_URL}/chat",
                json={
                    "message": f"Je veux un devis {insurance_type}",
                    "session_id": TEST_SESSION_ID + f"_quote_{insurance_type}"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                
                # Vérifier que le processus de devis a commencé
                quote_started = any(word in response_text.lower() for word in 
                                  ['devis', 'question', 'étape', 'âge', 'information'])
                
                print_test(f"Démarrage devis {insurance_type}", quote_started)
                if quote_started:
                    success_count += 1
                    
                    # Simuler quelques réponses pour tester le processus
                    test_responses = ["30 ans", "5 ans", "Tunis"]
                    
                    for test_response in test_responses:
                        try:
                            follow_up = requests.post(
                                f"{API_BASE_URL}/chat",
                                json={
                                    "message": test_response,
                                    "session_id": TEST_SESSION_ID + f"_quote_{insurance_type}"
                                },
                                timeout=30
                            )
                            
                            if follow_up.status_code == 200:
                                follow_data = follow_up.json()
                                follow_text = follow_data.get('response', '')
                                
                                # Vérifier la progression ou la génération du devis
                                if any(word in follow_text.lower() for word in 
                                      ['devis', 'prime', 'dt', 'garantie', 'question']):
                                    print_test(f"  Progression devis {insurance_type}", True)
                                    break
                                    
                        except Exception as e:
                            print_test(f"  Progression devis {insurance_type}", False, str(e))
                            break
            else:
                print_test(f"Démarrage devis {insurance_type}", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            print_test(f"Démarrage devis {insurance_type}", False, str(e))
    
    print(f"\n📊 Résultat: {success_count}/{len(insurance_types)} types de devis testés")
    return success_count >= len(insurance_types) * 0.8  # 80% de réussite minimum

def test_api_endpoints():
    """Test 5: Endpoints API spécialisés"""
    print_section("TEST 5: ENDPOINTS API SPÉCIALISÉS")
    
    endpoints = [
        ("/stats", "GET"),
        ("/database/info", "GET"),
        ("/client/<EMAIL>", "GET"),
        ("/client/1/contracts", "GET"),
        ("/client/1/claims", "GET"),
        ("/client/1/payments", "GET")
    ]
    
    success_count = 0
    
    for endpoint, method in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{API_BASE_URL}{endpoint}", timeout=10)
            else:
                response = requests.post(f"{API_BASE_URL}{endpoint}", timeout=10)
            
            success = response.status_code in [200, 404]  # 404 acceptable pour certains clients
            print_test(f"{method} {endpoint}", success, f"Status: {response.status_code}")
            
            if success:
                success_count += 1
                
        except Exception as e:
            print_test(f"{method} {endpoint}", False, str(e))
    
    print(f"\n📊 Résultat: {success_count}/{len(endpoints)} endpoints fonctionnels")
    return success_count >= len(endpoints) * 0.8  # 80% de réussite minimum

def main():
    """Fonction principale de test"""
    print("🚀 DÉMARRAGE DES TESTS COMPLETS BH ASSURANCE")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 API: {API_BASE_URL}")
    
    # Exécuter tous les tests
    tests = [
        ("Santé du serveur", test_server_health),
        ("Compréhension des produits", test_general_chat),
        ("Analyse des données clients", test_client_data_analysis),
        ("Génération de devis", test_quote_generation),
        ("Endpoints API", test_api_endpoints)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé final
    print_section("RÉSUMÉ FINAL")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n📊 SCORE GLOBAL: {passed}/{total} tests réussis ({passed/total*100:.1f}%)")
    
    if passed >= total * 0.8:
        print("🎉 SYSTÈME VALIDÉ - Prêt pour la démonstration !")
    else:
        print("⚠️  SYSTÈME PARTIELLEMENT FONCTIONNEL - Vérifications nécessaires")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
