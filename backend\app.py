from flask import Flask, request, jsonify, session
from flask_cors import CORS
from langchain_community.llms import LlamaCpp
import logging
import os
import uuid
from datetime import datetime
import json
from prompts import bh_prompts
from document_manager import doc_manager
from conversation_manager import conversation_manager

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bh_assurance_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, supports_credentials=True)
app.secret_key = 'bh-assurance-secret-key-2024'  # À changer en production

# Configuration
MODEL_PATH = r"C:\Users\<USER>\OneDrive\Bureau\genai-assistant\models\mistral-7b-instruct-v0.1.Q2_K.gguf"

# Initialisation du modèle
try:
    llm = LlamaCpp(
        model_path=MODEL_PATH,
        n_ctx=2048,
        temperature=0.7,
        verbose=False,
        max_tokens=512
    )
    logger.info("Modèle Mistral chargé avec succès")
except Exception as e:
    logger.error(f"Erreur lors du chargement du modèle: {e}")
    llm = None

# Stockage des conversations en mémoire (à remplacer par une base de données en production)
conversations = {}

class BHAssuranceBot:
    def __init__(self):
        self.company_info = {
            "name": "BH Assurance",
            "services": ["Assurance Auto", "Assurance Santé", "Assurance Habitation", "Assurance Vie"],
            "contact": "<EMAIL>",
            "phone": "+216 XX XXX XXX"
        }

    def get_specialized_prompt(self, user_message, conversation_history):
        """Génère un prompt spécialisé pour BH Assurance"""
        return bh_prompts.get_specialized_prompt(user_message, conversation_history)

    def _format_history(self, history):
        """Formate l'historique de conversation"""
        if not history:
            return "Aucun historique"

        formatted = []
        for entry in history[-5:]:  # Garde seulement les 5 derniers échanges
            formatted.append(f"Client: {entry.get('user', '')}")
            formatted.append(f"Assistant: {entry.get('bot', '')}")

        return "\n".join(formatted)

    def generate_response(self, user_message, session_id):
        """Génère une réponse personnalisée avec contexte documentaire"""
        if not llm:
            return "Désolé, le service est temporairement indisponible. Veuillez réessayer plus tard."

        # Récupérer l'historique de conversation
        conversation_history = conversations.get(session_id, [])

        # Récupérer le contexte documentaire
        contextual_info = doc_manager.get_contextual_info(user_message)

        # Générer le prompt spécialisé avec contexte
        base_prompt = bh_prompts.get_specialized_prompt(user_message, conversation_history)

        # Enrichir le prompt avec les informations contextuelles
        if contextual_info:
            enhanced_prompt = f"""{base_prompt}

INFORMATIONS CONTEXTUELLES DE LA BASE DE CONNAISSANCES:
{contextual_info}

Utilise ces informations pour enrichir ta réponse et être plus précis."""
        else:
            enhanced_prompt = base_prompt

        try:
            response = llm.invoke(enhanced_prompt)
            response_text = response.strip()

            # Sauvegarder dans l'historique
            conversation_entry = {
                "user": user_message,
                "bot": response_text,
                "timestamp": datetime.now().isoformat(),
                "context_used": bool(contextual_info)
            }

            if session_id not in conversations:
                conversations[session_id] = []
            conversations[session_id].append(conversation_entry)

            # Limiter l'historique à 20 échanges
            if len(conversations[session_id]) > 20:
                conversations[session_id] = conversations[session_id][-20:]

            logger.info(f"Réponse générée pour session {session_id} (contexte: {bool(contextual_info)})")
            return response_text

        except Exception as e:
            logger.error(f"Erreur lors de la génération de réponse: {e}")
            return "Je rencontre une difficulté technique. Un conseiller BH Assurance peut vous aider au +216 XX XXX XXX."

# Instance du bot
bot = BHAssuranceBot()

@app.route("/")
def home():
    return jsonify({
        "status": "✅ Agent Conversationnel BH Assurance est opérationnel",
        "company": "BH Assurance",
        "services": bot.company_info["services"],
        "version": "1.0.0"
    })

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        user_message = data.get('message', '').strip()
        session_id = data.get('session_id') or str(uuid.uuid4())

        if not user_message:
            return jsonify({'error': 'Message vide'}), 400

        # Générer la réponse
        response_text = bot.generate_response(user_message, session_id)

        return jsonify({
            'response': response_text,
            'session_id': session_id,
            'timestamp': datetime.now().isoformat(),
            'company': 'BH Assurance'
        })

    except Exception as e:
        logger.error(f"Erreur dans /chat: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/conversation/<session_id>', methods=['GET'])
def get_conversation(session_id):
    """Récupère l'historique d'une conversation"""
    conversation = conversations.get(session_id, [])
    return jsonify({
        'session_id': session_id,
        'conversation': conversation,
        'total_messages': len(conversation)
    })

@app.route('/health', methods=['GET'])
def health_check():
    """Vérification de l'état du service"""
    return jsonify({
        'status': 'healthy',
        'model_loaded': llm is not None,
        'active_sessions': len(conversations),
        'timestamp': datetime.now().isoformat()
    })

if __name__ == "__main__":
    logger.info("Démarrage de l'agent conversationnel BH Assurance")
    app.run(debug=True, port=5000, host='0.0.0.0')