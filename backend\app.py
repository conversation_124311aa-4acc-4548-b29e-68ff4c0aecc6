from flask import Flask, request, jsonify
from flask_cors import CORS
from langchain_community.llms import LlamaCpp

app = Flask(__name__)
CORS(app)

MODEL_PATH = r"C:\Users\<USER>\OneDrive\Bureau\genai-assistant\models\mistral-7b-instruct-v0.1.Q2_K.gguf"

llm = LlamaCpp(
    model_path=MODEL_PATH,
    n_ctx=2048,
    temperature=0.7,
    verbose=True
)

@app.route("/")
def home():
    return "✅ Serveur GenAI local avec Mistral est démarré."

@app.route('/chat', methods=['POST'])
def chat():
    data = request.json
    user_message = data.get('message', '')

    if not user_message:
        return jsonify({'error': 'No message provided'}), 400

    # Construire un prompt simple, tu peux améliorer le prompt ici
    prompt = f"Tu es un assistant. Réponds simplement à ce message : {user_message}"

    try:
        # Appel au modèle avec une limite de tokens
        response = llm.invoke(prompt)
        # Nettoyage basique si nécessaire
        response_text = response.strip()
    except Exception as e:
        return jsonify({'error': str(e)}), 500

    return jsonify({'response': response_text})

if __name__ == "__main__":
    app.run(debug=True, port=5000)