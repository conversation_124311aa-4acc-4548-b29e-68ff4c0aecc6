from flask import Flask, request, jsonify, session
from flask_cors import CORS
from langchain_community.llms import LlamaCpp
import logging
import os
import uuid
from datetime import datetime
import json
from prompts import bh_prompts
from document_manager import doc_manager
from conversation_manager import conversation_manager
from database_manager import db_manager
from client_analyzer import client_analyzer
from quote_generator import quote_generator

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bh_assurance_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, supports_credentials=True)
app.secret_key = 'bh-assurance-secret-key-2024'  # À changer en production

# Configuration
MODEL_PATH = r"C:\Users\<USER>\OneDrive\Bureau\genai-assistant\models\mistral-7b-instruct-v0.1.Q2_K.gguf"

# Initialisation du modèle
try:
    llm = LlamaCpp(
        model_path=MODEL_PATH,
        n_ctx=2048,
        temperature=0.7,
        verbose=False,
        max_tokens=512
    )
    logger.info("Modèle Mistral chargé avec succès")
except Exception as e:
    logger.error(f"Erreur lors du chargement du modèle: {e}")
    llm = None

# Stockage des conversations maintenant géré par conversation_manager

class BHAssuranceBot:
    def __init__(self):
        self.company_info = {
            "name": "BH Assurance",
            "services": ["Assurance Auto", "Assurance Santé", "Assurance Habitation", "Assurance Vie"],
            "contact": "<EMAIL>",
            "phone": "+216 XX XXX XXX"
        }
        self.current_client = {}  # Stockage temporaire du client identifié
        self.quote_sessions = {}  # Sessions de devis en cours

    def get_specialized_prompt(self, user_message, conversation_history):
        """Génère un prompt spécialisé pour BH Assurance"""
        return bh_prompts.get_specialized_prompt(user_message, conversation_history)

    def _format_history(self, history):
        """Formate l'historique de conversation"""
        if not history:
            return "Aucun historique"

        formatted = []
        for entry in history[-5:]:  # Garde seulement les 5 derniers échanges
            formatted.append(f"Client: {entry.get('user', '')}")
            formatted.append(f"Assistant: {entry.get('bot', '')}")

        return "\n".join(formatted)

    def generate_response(self, user_message, session_id):
        """Génère une réponse personnalisée avec toutes les fonctionnalités"""
        if not llm:
            return "Désolé, le service est temporairement indisponible. Veuillez réessayer plus tard."

        # 1. Vérifier si c'est une demande de devis en cours
        if session_id in self.quote_sessions:
            return self._handle_quote_process(user_message, session_id)

        # 2. Détecter si c'est une demande de devis
        if self._is_quote_request(user_message):
            return self._start_quote_process(user_message, session_id)

        # 3. Identifier le client si mentionné
        client = client_analyzer.identify_client(user_message)
        if client:
            self.current_client[session_id] = client
            return self._handle_client_query(user_message, session_id, client)

        # 4. Si client déjà identifié, traiter les requêtes client
        if session_id in self.current_client:
            return self._handle_client_query(user_message, session_id, self.current_client[session_id])

        # 5. Réponse générale avec contexte documentaire
        return self._generate_general_response(user_message, session_id)

    def _is_quote_request(self, message: str) -> bool:
        """Détecte si le message est une demande de devis"""
        quote_keywords = ["devis", "tarif", "prix", "cotisation", "souscrire", "combien coûte"]
        return any(keyword in message.lower() for keyword in quote_keywords)

    def _start_quote_process(self, message: str, session_id: str) -> str:
        """Démarre le processus de devis"""
        # Détecter le type d'assurance
        insurance_type = self._detect_insurance_type(message)

        if not insurance_type:
            return """Pour vous établir un devis personnalisé, j'ai besoin de savoir quel type d'assurance vous intéresse :

🚗 **Assurance Auto** - Tapez "devis auto"
🏠 **Assurance Habitation** - Tapez "devis habitation"
🏥 **Assurance Santé** - Tapez "devis santé"
💼 **Assurance Vie** - Tapez "devis vie"

Quel type d'assurance souhaitez-vous ?"""

        # Démarrer la session de devis
        quote_result = quote_generator.start_quote_session(session_id, insurance_type)
        self.quote_sessions[session_id] = insurance_type

        if "error" in quote_result:
            return "Désolé, je ne peux pas démarrer le devis pour le moment. Veuillez réessayer."

        return f"""🎯 **Devis {insurance_type.title()}**

{quote_result['question']}

📊 Étape {quote_result['step']}/{quote_result['total_steps']}"""

    def _handle_quote_process(self, message: str, session_id: str) -> str:
        """Gère le processus de devis en cours"""
        result = quote_generator.process_quote_response(session_id, message)

        if result.get("status") == "question":
            return f"""📋 **Question suivante :**

{result['question']}

📊 Étape {result['step']}/{result['total_steps']}"""

        elif result.get("status") == "quote_generated":
            # Nettoyer la session de devis
            if session_id in self.quote_sessions:
                del self.quote_sessions[session_id]

            return self._format_quote_response(result)

        elif result.get("status") == "clarification_needed":
            return f"""❓ {result['message']}

**Question actuelle :** {result['current_question']}"""

        else:
            return "Une erreur s'est produite lors du traitement de votre devis. Veuillez recommencer."

    def _detect_insurance_type(self, message: str) -> str:
        """Détecte le type d'assurance demandé"""
        message_lower = message.lower()

        if any(word in message_lower for word in ["auto", "voiture", "véhicule", "automobile"]):
            return "auto"
        elif any(word in message_lower for word in ["habitation", "maison", "appartement", "logement"]):
            return "habitation"
        elif any(word in message_lower for word in ["santé", "médical", "maladie", "hospitalisation"]):
            return "sante"
        elif any(word in message_lower for word in ["vie", "décès", "capital"]):
            return "vie"

        return ""

    def _handle_client_query(self, message: str, session_id: str, client: dict) -> str:
        """Gère les requêtes spécifiques à un client identifié"""
        client_id = client['client_id']
        message_lower = message.lower()

        # Analyser le type de requête
        if any(word in message_lower for word in ["contrat", "garantie", "couverture"]):
            analysis = client_analyzer.analyze_client_contracts(client_id)
            return self._format_contracts_response(analysis, client)

        elif any(word in message_lower for word in ["sinistre", "réclamation", "dommage"]):
            analysis = client_analyzer.analyze_client_claims(client_id)
            return self._format_claims_response(analysis, client)

        elif any(word in message_lower for word in ["paiement", "facture", "échéance", "cotisation"]):
            analysis = client_analyzer.analyze_payment_status(client_id)
            return self._format_payments_response(analysis, client)

        elif "résumé" in message_lower or "situation" in message_lower:
            summary = client_analyzer.get_client_summary(client_id)
            return self._format_client_summary(summary)

        else:
            # Réponse générale avec contexte client
            return self._generate_general_response(message, session_id, client_context=client)

    def _generate_general_response(self, user_message, session_id, client_context=None):
        """Génère une réponse générale avec contexte documentaire"""
        # Récupérer l'historique de conversation
        conversation_history = conversation_manager.get_conversation_history(session_id, limit=10)

        # Récupérer le contexte documentaire
        contextual_info = doc_manager.get_contextual_info(user_message)

        # Générer le prompt spécialisé avec contexte
        base_prompt = bh_prompts.get_specialized_prompt(user_message, conversation_history)

        # Enrichir avec le contexte client si disponible
        if client_context:
            client_info = f"\n\nCLIENT IDENTIFIÉ: {client_context['prenom']} {client_context['nom']} ({client_context['email']})"
            base_prompt += client_info

        # Enrichir le prompt avec les informations contextuelles
        if contextual_info:
            enhanced_prompt = f"""{base_prompt}

INFORMATIONS CONTEXTUELLES DE LA BASE DE CONNAISSANCES:
{contextual_info}

Utilise ces informations pour enrichir ta réponse et être plus précis."""
        else:
            enhanced_prompt = base_prompt

        try:
            response = llm.invoke(enhanced_prompt)
            response_text = response.strip()

            # Sauvegarder les messages dans la base de données
            conversation_manager.add_message(
                session_id,
                'user',
                user_message,
                {
                    'context_available': bool(contextual_info),
                    'client_identified': bool(client_context)
                }
            )

            conversation_manager.add_message(
                session_id,
                'bot',
                response_text,
                {
                    'context_used': bool(contextual_info),
                    'client_context': bool(client_context)
                }
            )

            logger.info(f"Réponse générée pour session {session_id} (contexte: {bool(contextual_info)}, client: {bool(client_context)})")
            return response_text

        except Exception as e:
            logger.error(f"Erreur lors de la génération de réponse: {e}")
            return "Je rencontre une difficulté technique. Un conseiller BH Assurance peut vous aider au +216 XX XXX XXX."

    def _format_quote_response(self, quote_data: dict) -> str:
        """Formate la réponse de devis"""
        pricing = quote_data['pricing']
        guarantees = quote_data['guarantees']

        response = f"""🎉 **Votre Devis BH Assurance**

📋 **Référence :** {quote_data['quote_id']}
🏷️ **Type :** {quote_data['insurance_type'].title()}

💰 **TARIFICATION**
• Prime annuelle : {pricing['prime_annuelle']} DT
• Prime mensuelle : {pricing['prime_mensuelle']} DT
• Franchise : {pricing['franchise']} DT
• Plafond de garantie : {pricing['plafond_garantie']} DT

🛡️ **GARANTIES INCLUSES**
"""
        for guarantee in guarantees:
            response += f"• {guarantee}\n"

        response += f"""
⏰ **Validité :** {quote_data['validity']}

📞 **Pour souscrire :** Contactez-nous au +216 71 XXX XXX
📧 **Email :** <EMAIL>

✨ Ce devis est personnalisé selon vos informations. Nos conseillers peuvent vous accompagner pour finaliser votre souscription."""

        return response

    def _format_contracts_response(self, analysis: dict, client: dict) -> str:
        """Formate la réponse d'analyse des contrats"""
        if analysis['status'] != 'success':
            return f"❌ {analysis['message']}"

        response = f"""👤 **Contrats de {client['prenom']} {client['nom']}**

📊 **RÉSUMÉ**
• Nombre total de contrats : {analysis['total_contracts']}
• Contrats actifs : {analysis['active_contracts']}
• Prime totale annuelle : {analysis['total_premium']} DT

📋 **DÉTAIL DES CONTRATS**
"""

        for contract in analysis['contracts_detail']:
            response += f"""
🔹 **{contract['numero']}** - {contract['produit']}
   • Formule : {contract['formule']}
   • Statut : {contract['statut']}
   • Prime : {contract['prime_annuelle']} DT/an
   • Période : {contract['date_debut']} → {contract['date_fin']}
   • Franchise : {contract['franchise']} DT
"""

        return response

    def _format_claims_response(self, analysis: dict, client: dict) -> str:
        """Formate la réponse d'analyse des sinistres"""
        if analysis['status'] == 'no_claims':
            return f"✅ **{client['prenom']} {client['nom']}** n'a aucun sinistre déclaré."

        if analysis['status'] != 'success':
            return f"❌ {analysis['message']}"

        response = f"""🚨 **Sinistres de {client['prenom']} {client['nom']}**

📊 **RÉSUMÉ**
• Nombre total de sinistres : {analysis['total_claims']}
• Montant total des dommages : {analysis['total_damage']} DT
• Montant indemnisé : {analysis['total_indemnified']} DT

📋 **DÉTAIL DES SINISTRES**
"""

        for claim in analysis['claims_detail']:
            status_emoji = "✅" if claim['statut'] == 'regle' else "⏳" if claim['statut'] == 'en_cours' else "🔍"

            response += f"""
{status_emoji} **{claim['numero']}** - {claim['type']}
   • Date : {claim['date_sinistre']}
   • Description : {claim['description']}
   • Dommages : {claim['montant_dommage']} DT
   • Indemnisé : {claim['montant_indemnise']} DT
   • Statut : {claim['statut']}
"""

        return response

    def _format_payments_response(self, analysis: dict, client: dict) -> str:
        """Formate la réponse d'analyse des paiements"""
        if analysis['status'] != 'success':
            return f"❌ {analysis['message']}"

        response = f"""💳 **Paiements de {client['prenom']} {client['nom']}**

📊 **RÉSUMÉ**
• Montant payé : {analysis['paid_amount']} DT ✅
• En attente : {analysis['pending_amount']} DT ⏳
• En retard : {analysis['overdue_amount']} DT ⚠️

📋 **DÉTAIL DES PAIEMENTS**
"""

        for payment in analysis['payments_detail']:
            if payment['statut'] == 'paye':
                status_emoji = "✅"
            elif payment['statut'] == 'en_retard':
                status_emoji = "⚠️"
            else:
                status_emoji = "⏳"

            response += f"""
{status_emoji} **{payment['contrat']}** - {payment['produit']}
   • Montant : {payment['montant']} DT
   • Échéance : {payment['date_echeance']}
   • Statut : {payment['statut']}
"""

            if payment['jours_retard'] > 0:
                response += f"   • ⚠️ Retard : {payment['jours_retard']} jours\n"

        return response

    def _format_client_summary(self, summary: dict) -> str:
        """Formate le résumé client"""
        if summary['status'] != 'success':
            return f"❌ {summary['message']}"

        client_info = summary['client_info']
        contracts = summary['contracts_summary']
        claims = summary['claims_summary']
        payments = summary['payments_summary']

        return f"""👤 **Résumé Client - {client_info['prenom']} {client_info['nom']}**

📞 **Contact :** {client_info['telephone']}
📧 **Email :** {client_info['email']}
📊 **Statut :** {client_info['statut']}

📋 **CONTRATS**
• Total : {contracts['total']} contrats
• Actifs : {contracts['actifs']} contrats
• Prime totale : {contracts['prime_totale']} DT/an

🚨 **SINISTRES**
• Total : {claims['total']} sinistres
• Dommages : {claims['montant_total_dommages']} DT
• Indemnisé : {claims['montant_indemnise']} DT

💳 **PAIEMENTS**
• Payé : {payments['montant_paye']} DT ✅
• En attente : {payments['montant_en_attente']} DT ⏳
• En retard : {payments['montant_en_retard']} DT ⚠️

📞 **Besoin d'aide ?** Contactez votre conseiller au +216 71 XXX XXX"""

# Instance du bot
bot = BHAssuranceBot()

@app.route("/")
def home():
    return jsonify({
        "status": "✅ Agent Conversationnel BH Assurance est opérationnel",
        "company": "BH Assurance",
        "services": bot.company_info["services"],
        "version": "1.0.0"
    })

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        user_message = data.get('message', '').strip()
        session_id = data.get('session_id') or str(uuid.uuid4())

        if not user_message:
            return jsonify({'error': 'Message vide'}), 400

        # Générer la réponse
        response_text = bot.generate_response(user_message, session_id)

        return jsonify({
            'response': response_text,
            'session_id': session_id,
            'timestamp': datetime.now().isoformat(),
            'company': 'BH Assurance'
        })

    except Exception as e:
        logger.error(f"Erreur dans /chat: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/conversation/<session_id>', methods=['GET'])
def get_conversation(session_id):
    """Récupère l'historique d'une conversation"""
    conversation = conversation_manager.get_conversation_history(session_id)
    session_info = conversation_manager.get_session_info(session_id)

    return jsonify({
        'session_id': session_id,
        'conversation': conversation,
        'session_info': session_info,
        'total_messages': len(conversation)
    })

@app.route('/health', methods=['GET'])
def health_check():
    """Vérification de l'état du service"""
    stats = conversation_manager.get_conversation_stats()

    return jsonify({
        'status': 'healthy',
        'model_loaded': llm is not None,
        'conversation_stats': stats,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/stats', methods=['GET'])
def get_stats():
    """Récupère les statistiques détaillées"""
    stats = conversation_manager.get_conversation_stats()
    active_sessions = conversation_manager.get_active_sessions(24)

    return jsonify({
        'statistics': stats,
        'active_sessions': active_sessions,
        'server_info': {
            'model_loaded': llm is not None,
            'uptime': datetime.now().isoformat()
        }
    })

@app.route('/client/<identifier>', methods=['GET'])
def get_client_info(identifier):
    """Récupère les informations d'un client"""
    try:
        client = client_analyzer.identify_client(identifier)
        if not client:
            return jsonify({'error': 'Client non trouvé'}), 404

        summary = client_analyzer.get_client_summary(client['client_id'])
        return jsonify(summary)

    except Exception as e:
        logger.error(f"Erreur lors de la récupération client: {e}")
        return jsonify({'error': 'Erreur interne'}), 500

@app.route('/client/<int:client_id>/contracts', methods=['GET'])
def get_client_contracts(client_id):
    """Récupère les contrats d'un client"""
    try:
        analysis = client_analyzer.analyze_client_contracts(client_id)
        return jsonify(analysis)

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des contrats: {e}")
        return jsonify({'error': 'Erreur interne'}), 500

@app.route('/client/<int:client_id>/claims', methods=['GET'])
def get_client_claims(client_id):
    """Récupère les sinistres d'un client"""
    try:
        analysis = client_analyzer.analyze_client_claims(client_id)
        return jsonify(analysis)

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des sinistres: {e}")
        return jsonify({'error': 'Erreur interne'}), 500

@app.route('/client/<int:client_id>/payments', methods=['GET'])
def get_client_payments(client_id):
    """Récupère les paiements d'un client"""
    try:
        analysis = client_analyzer.analyze_payment_status(client_id)
        return jsonify(analysis)

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des paiements: {e}")
        return jsonify({'error': 'Erreur interne'}), 500

@app.route('/quote/start', methods=['POST'])
def start_quote():
    """Démarre un processus de devis"""
    try:
        data = request.json
        session_id = data.get('session_id', str(uuid.uuid4()))
        insurance_type = data.get('insurance_type')

        if not insurance_type:
            return jsonify({'error': 'Type d\'assurance requis'}), 400

        result = quote_generator.start_quote_session(session_id, insurance_type)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Erreur lors du démarrage de devis: {e}")
        return jsonify({'error': 'Erreur interne'}), 500

@app.route('/quote/respond', methods=['POST'])
def respond_quote():
    """Traite une réponse dans le processus de devis"""
    try:
        data = request.json
        session_id = data.get('session_id')
        response = data.get('response')

        if not session_id or not response:
            return jsonify({'error': 'Session ID et réponse requis'}), 400

        result = quote_generator.process_quote_response(session_id, response)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Erreur lors du traitement de réponse devis: {e}")
        return jsonify({'error': 'Erreur interne'}), 500

@app.route('/database/info', methods=['GET'])
def get_database_info():
    """Récupère des informations sur la base de données"""
    try:
        # Compter les enregistrements dans chaque table
        import sqlite3
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()

            tables_info = {}
            tables = ['clients', 'contrats', 'sinistres', 'paiements', 'produits', 'garanties']

            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                tables_info[table] = count

        return jsonify({
            'database_path': db_manager.db_path,
            'tables_info': tables_info,
            'last_updated': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des infos DB: {e}")
        return jsonify({'error': 'Erreur interne'}), 500

if __name__ == "__main__":
    logger.info("Démarrage de l'agent conversationnel BH Assurance")
    print("🚀 Serveur BH Assurance démarré sur http://127.0.0.1:5000")
    app.run(debug=False, port=5000, host='127.0.0.1')